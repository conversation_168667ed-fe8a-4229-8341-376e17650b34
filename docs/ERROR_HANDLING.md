# Error Handling Guide

## 🚨 **Problem: TypeError: Failed to fetch**

### **Penyebab Umum:**

1. **Network Issues**
   - Koneksi internet terputus
   - Server API tidak dapat diakses
   - Firewall memblokir request

2. **Configuration Issues**
   - Environment variable `NEXT_PUBLIC_API_URL` tidak di-set
   - URL API salah atau tidak valid
   - CORS policy issues

3. **Server Issues**
   - API server down
   - Database connection error
   - Server overload

## ✅ **Solusi yang Telah Diimplementasi**

### **1. <PERSON><PERSON><PERSON> (`src/lib/api/errorHandler.ts`)**

```typescript
import { apiRequestWithRetry, getErrorMessage } from '@/lib/api/errorHandler';

// Automatic retry with exponential backoff
const data = await apiRequestWithRetry('/api/users', {}, 3, 1000);

// User-friendly error messages
const errorMessage = getErrorMessage(error);
```

**Features:**
- ✅ Automatic retry mechanism
- ✅ Timeout handling (30 seconds)
- ✅ Network connectivity check
- ✅ User-friendly error messages
- ✅ Different error types handling

### **2. Error <PERSON>undary (`src/components/ui/base/ErrorBoundary.tsx`)**

```typescript
import ErrorBoundary from '@/components/ui/base/ErrorBoundary';

<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>
```

**Features:**
- ✅ Catches JavaScript errors in component tree
- ✅ Displays user-friendly error page
- ✅ Retry and navigation options
- ✅ Development error details

### **3. API Error Hook (`src/hooks/useApiError.ts`)**

```typescript
import { useApiError, useApiOperation } from '@/hooks/useApiError';

// Basic error handling
const { executeWithErrorHandling } = useApiError();
const result = await executeWithErrorHandling(() => fetchData());

// Advanced operation handling
const { execute, isLoading, error } = useApiOperation();
await execute(() => fetchData(), {
  onSuccess: (data) => console.log('Success:', data),
  showSuccessToast: true,
  successMessage: 'Data berhasil dimuat'
});
```

### **4. Custom 404 Page (`src/app/not-found.tsx`)**

- ✅ User-friendly 404 page
- ✅ Navigation options
- ✅ Quick links to common pages
- ✅ Help section

### **5. System Status Monitor (`src/components/ui/base/SystemStatus.tsx`)**

```typescript
import SystemStatus, { SystemStatusIndicator } from '@/components/ui/base/SystemStatus';

// Full status check
<SystemStatus onRetry={handleRetry} showDetails={true} />

// Compact indicator
<SystemStatusIndicator />
```

## 🔧 **Implementation Guide**

### **Step 1: Update API Calls**

**Before:**
```typescript
// ❌ No error handling
const response = await fetch('/api/data');
const data = await response.json();
```

**After:**
```typescript
// ✅ With error handling
import { apiRequestWithRetry } from '@/lib/api/errorHandler';

try {
  const data = await apiRequestWithRetry('/api/data');
  // Handle success
} catch (error) {
  // Error is automatically handled
  console.error('Failed to fetch data:', error);
}
```

### **Step 2: Wrap Components with Error Boundary**

```typescript
// Layout or page level
<ErrorBoundary>
  <YourPageComponent />
</ErrorBoundary>
```

### **Step 3: Use Error Hooks in Components**

```typescript
function MyComponent() {
  const { executeWithErrorHandling, isLoading, error } = useApiError({
    showToast: true,
    redirectOnUnauthorized: true
  });

  const handleFetchData = async () => {
    const result = await executeWithErrorHandling(async () => {
      return await fetchMyData();
    });
    
    if (result) {
      // Handle success
      setData(result);
    }
  };

  if (error) {
    return <LoadingErrorComponent error={error.message} retry={handleFetchData} />;
  }

  return (
    <div>
      {/* Your component content */}
    </div>
  );
}
```

### **Step 4: Environment Configuration**

Create `.env.local`:
```bash
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_APP_NAME=SimTanaman
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 🔍 **Debugging Guide**

### **Check Environment Variables**

```typescript
import { checkEnvironmentVariables } from '@/lib/utils/environment';

const envCheck = checkEnvironmentVariables();
if (!envCheck.isValid) {
  console.error('Environment errors:', envCheck.errors);
}
```

### **Test API Connectivity**

```typescript
import { checkApiHealth } from '@/lib/api/errorHandler';

const isApiHealthy = await checkApiHealth();
console.log('API Status:', isApiHealthy ? 'OK' : 'Failed');
```

### **System Health Check**

```typescript
import { performSystemCheck } from '@/lib/utils/environment';

const status = await performSystemCheck();
console.log('System Status:', status);
```

## 🚀 **Best Practices**

### **1. Always Use Error Boundaries**
```typescript
// ✅ Good
<ErrorBoundary>
  <DataTable />
</ErrorBoundary>

// ❌ Bad - no error boundary
<DataTable />
```

### **2. Implement Retry Logic**
```typescript
// ✅ Good - with retry
const data = await apiRequestWithRetry('/api/data', {}, 3);

// ❌ Bad - no retry
const response = await fetch('/api/data');
```

### **3. Show Loading States**
```typescript
// ✅ Good
if (isLoading) return <LoadingSkeleton />;
if (error) return <ErrorComponent />;
return <DataComponent data={data} />;
```

### **4. Handle Different Error Types**
```typescript
// ✅ Good
try {
  const data = await fetchData();
} catch (error) {
  if (error.status === 401) {
    // Handle unauthorized
    router.push('/login');
  } else if (error.status === 404) {
    // Handle not found
    router.push('/404');
  } else {
    // Handle other errors
    showErrorToast(error.message);
  }
}
```

## 📋 **Error Types & Solutions**

| Error Type | Cause | Solution |
|------------|-------|----------|
| `TypeError: Failed to fetch` | Network/API issues | Use `apiRequestWithRetry` |
| `401 Unauthorized` | Invalid/expired token | Redirect to login |
| `404 Not Found` | Resource not found | Show 404 page |
| `500 Server Error` | Server issues | Show retry option |
| `CORS Error` | Cross-origin policy | Configure server CORS |
| `Timeout Error` | Slow response | Increase timeout, show loading |

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **API URL not configured**
   ```bash
   # Add to .env.local
   NEXT_PUBLIC_API_URL=your_api_url_here
   ```

2. **CORS issues**
   ```typescript
   // Server-side CORS configuration needed
   app.use(cors({
     origin: ['http://localhost:3000'],
     credentials: true
   }));
   ```

3. **Network connectivity**
   ```typescript
   // Check network status
   const isOnline = await checkNetworkConnectivity();
   ```

## 📱 **User Experience**

### **Error Messages (Indonesian)**
- Network Error: "Tidak dapat terhubung ke server"
- Timeout: "Server membutuhkan waktu terlalu lama"
- Unauthorized: "Sesi Anda telah berakhir"
- Not Found: "Data yang diminta tidak ditemukan"
- Server Error: "Terjadi kesalahan pada server"

### **Recovery Options**
- ✅ Retry button
- ✅ Go back navigation
- ✅ Home page redirect
- ✅ Manual refresh option
- ✅ Contact support info

## 🎯 **Testing Error Handling**

### **Simulate Network Errors**
```typescript
// In development, simulate errors
if (process.env.NODE_ENV === 'development') {
  // Simulate network error
  throw new Error('Failed to fetch');
}
```

### **Test Error Boundaries**
```typescript
// Create error component for testing
function ErrorTrigger() {
  throw new Error('Test error');
}

// Wrap in error boundary
<ErrorBoundary>
  <ErrorTrigger />
</ErrorBoundary>
```

Dengan implementasi ini, aplikasi Anda akan lebih robust dalam menangani error "Failed to fetch" dan memberikan pengalaman pengguna yang lebih baik! 🎉
