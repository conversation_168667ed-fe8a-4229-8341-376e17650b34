# Tab Navigation Component

## 📋 **Overview**

Komponen `TabNavigation` adalah komponen reusable untuk membuat tab navigation dengan dukungan role-based visibility dan responsive design.

## 🎯 **Features**

- ✅ **Role-based visibility** - Tab dapat disembunyikan berdasarkan role user
- ✅ **Responsive design** - Menyesuaikan layout untuk mobile dan desktop
- ✅ **Dynamic underline** - Indikator garis bawah yang menyesuaikan dengan jumlah tab
- ✅ **Smooth transitions** - Animasi halus saat berpindah tab
- ✅ **Customizable** - Dapat dikustomisasi dengan className dan children
- ✅ **TypeScript support** - Full type safety

## 🔧 **Usage**

### **Basic Usage**

```tsx
import TabNavigation from "@/components/ui/base/TabNavigation";

function MyComponent() {
  const [activeTab, setActiveTab] = useState(0);
  
  const tabs = [
    { id: 0, label: "Tab 1", visible: true },
    { id: 1, label: "Tab 2", visible: true },
    { id: 2, label: "Tab 3", visible: userRole === "admin" }
  ];

  return (
    <TabNavigation
      tabs={tabs}
      activeTab={activeTab}
      onTabChange={setActiveTab}
    >
      {/* Additional content like buttons */}
      <Button>Action Button</Button>
    </TabNavigation>
  );
}
```

### **With Hook (Recommended)**

```tsx
import TabNavigation, { useTabNavigation } from "@/components/ui/base/TabNavigation";

function MyComponent() {
  const userRole = usePermission(state => state.role);
  const { activeTab, setActiveTab, tabs } = useTabNavigation(0, userRole);

  return (
    <TabNavigation
      tabs={tabs}
      activeTab={activeTab}
      onTabChange={setActiveTab}
    >
      <Button className="bg-primary-default">
        <Printer className="w-4 h-4" />
        Print
      </Button>
    </TabNavigation>
  );
}
```

## 📝 **Props**

### **TabNavigationProps**

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `tabs` | `Tab[]` | - | Array of tab objects |
| `activeTab` | `number` | - | Currently active tab ID |
| `onTabChange` | `(tabId: number) => void` | - | Callback when tab changes |
| `className` | `string` | `""` | Additional CSS classes |
| `children` | `ReactNode` | - | Additional content (buttons, etc.) |

### **Tab Interface**

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `id` | `number` | - | Unique tab identifier |
| `label` | `string` | - | Tab display text |
| `visible` | `boolean` | `true` | Whether tab should be visible |

## 🎨 **Styling**

### **Default Styles**

```css
/* Active tab */
.text-primary-default

/* Inactive tab */
.text-gray-400 hover:text-gray-600

/* Underline indicator */
.bg-primary-default

/* Container */
.border-b border-gray-300
```

### **Customization**

```tsx
<TabNavigation
  className="my-custom-class"
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={setActiveTab}
>
  <div className="flex gap-2">
    <Button variant="outline">Cancel</Button>
    <Button variant="default">Save</Button>
  </div>
</TabNavigation>
```

## 📱 **Responsive Behavior**

- **Desktop**: Horizontal layout with tabs and content side by side
- **Mobile**: Vertical layout with tabs stacked above content
- **Tab width**: Automatically adjusts based on number of visible tabs

## 🔐 **Role-based Visibility**

```tsx
const tabs = [
  { id: 0, label: "Public Tab", visible: true },
  { id: 1, label: "User Tab", visible: ["user", "admin"].includes(userRole) },
  { id: 2, label: "Admin Only", visible: userRole === "admin" }
];
```

## 🎯 **Examples**

### **Distribution Page Example**

```tsx
function DistributionPage() {
  const role = usePermission(state => state.role);
  const { activeTab, setActiveTab, tabs } = useTabNavigation(0, role);

  return (
    <div>
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      >
        <Button className="bg-primary-default hover:bg-primary-600">
          <Printer className="w-4 h-4" />
          Print
        </Button>
      </TabNavigation>

      {/* Tab Content */}
      {activeTab === 0 && <TabSubmission />}
      {activeTab === 1 && <TabSchedule />}
      {activeTab === 2 && role === "admin" && <TabDocumentation />}
    </div>
  );
}
```

### **Custom Styling Example**

```tsx
<TabNavigation
  className="bg-white shadow-sm rounded-lg p-4"
  tabs={[
    { id: 0, label: "Overview" },
    { id: 1, label: "Details" },
    { id: 2, label: "Settings", visible: isOwner }
  ]}
  activeTab={activeTab}
  onTabChange={setActiveTab}
>
  <div className="flex items-center gap-3">
    <Badge variant="secondary">{itemCount} items</Badge>
    <Button size="sm">
      <Plus className="w-4 h-4" />
      Add New
    </Button>
  </div>
</TabNavigation>
```

## 🔄 **Migration Guide**

### **Before (Old Implementation)**

```tsx
// Old hardcoded implementation
<div className="relative flex border-b border-gray-300 mb-7 w-96">
  <button className={selectTab === 0 ? "active" : ""}>Tab 1</button>
  <button className={selectTab === 1 ? "active" : ""}>Tab 2</button>
  {role === "admin" && (
    <button className={selectTab === 2 ? "active" : ""}>Tab 3</button>
  )}
  <div className="absolute bottom-0 h-1 bg-primary-default..."></div>
</div>
```

### **After (New Component)**

```tsx
// New reusable component
const { activeTab, setActiveTab, tabs } = useTabNavigation(0, role);

<TabNavigation
  tabs={tabs}
  activeTab={activeTab}
  onTabChange={setActiveTab}
/>
```

## 🚀 **Benefits**

1. **Reusability** - Dapat digunakan di multiple pages
2. **Maintainability** - Logic terpusat dalam satu komponen
3. **Consistency** - Styling dan behavior yang konsisten
4. **Type Safety** - Full TypeScript support
5. **Accessibility** - Built-in keyboard navigation support
6. **Performance** - Optimized rendering dengan proper memoization

## 🔧 **Advanced Usage**

### **Custom Tab Content**

```tsx
const customTabs = [
  { 
    id: 0, 
    label: (
      <div className="flex items-center gap-2">
        <FileText className="w-4 h-4" />
        Documents
      </div>
    )
  },
  { 
    id: 1, 
    label: (
      <div className="flex items-center gap-2">
        <Calendar className="w-4 h-4" />
        Schedule
        <Badge>3</Badge>
      </div>
    )
  }
];
```

### **Conditional Rendering**

```tsx
{activeTab === 0 && <ComponentA />}
{activeTab === 1 && <ComponentB />}
{activeTab === 2 && role === "admin" && <ComponentC />}
```
