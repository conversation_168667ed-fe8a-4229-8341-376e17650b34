import type { Metadata } from "next";
import "./globals.css";
import QueryProvider from "./QueryProvider";
import { Poppins } from "next/font/google";
import "animate.css";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import { AuthProvider } from "@/contexts/AuthContext";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Sistem Informasi Manajemen Tani",
  description: "Sistem Informasi Manajemen Tani",
};

// Optimize font loading
const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap", // Improve font loading performance
  preload: true,
});

// Loading component
function PageLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        <p className="text-sm text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id" className={poppins.variable}>
      <head>
        {/* Preload critical resources */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        {/* Optimize viewport for mobile */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${poppins.className} text-base text-foreground bg-background antialiased`}>
        <Suspense fallback={<PageLoading />}>
          <AuthProvider>
            <QueryProvider>
              <Suspense fallback={<PageLoading />}>
                {children}
              </Suspense>
            </QueryProvider>
          </AuthProvider>
        </Suspense>

        {/* Optimize ToastContainer */}
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          limit={3}
          toastClassName="text-sm"
        />
      </body>
    </html>
  );
}
