@tailwind base;
@tailwind components;
@tailwind utilities;
@import "leaflet/dist/leaflet.css";
/* Default */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10% !important;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 173 58% 39%;
    --chart-2: 12 76% 61%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-5: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-2: 340 75% 55%;
  }
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Black.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Black.woff") format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Bold.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-BlackItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-BlackItalic.woff") format("woff");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-BoldItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-BoldItalic.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-ExtraBoldItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-ExtraBoldItalic.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-ExtraBold.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-ExtraBold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-ExtraLight.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-ExtraLight.woff") format("woff");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Light.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Italic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Italic.woff") format("woff");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-LightItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-LightItalic.woff") format("woff");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-ExtraLightItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-ExtraLightItalic.woff") format("woff");
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-MediumItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-MediumItalic.woff") format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Medium.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Regular.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-SemiBold.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-SemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-ThinItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-ThinItalic.woff") format("woff");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-SemiBoldItalic.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-SemiBoldItalic.woff") format("woff");
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src:
    url("/assets/fonts/poppins/Poppins-Thin.woff2") format("woff2"),
    url("/assets/fonts/poppins/Poppins-Thin.woff") format("woff");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

body {
  font-family: "Poppins", sans-serif !important;
}

.pointer-events-none {
  background-color: white;
}

.aria-selected\:opacity-100[aria-selected="true"] {
  background-color: #597445ad;
  color: white;
  border-radius: inherit;
}

.ql-editor.ql-blank {
  min-height: 200px;
}

.rdp-vhidden {
  display: none;
}

.rdp-dropdown_month div {
  display: none;
}

.rdp-dropdown_year div {
  display: none;
}

.rdp-caption_dropdowns {
  display: flex;
}

.rdp-dropdown_month {
  width: 57%;
}

input:focus-visible,
select:focus-visible,
button:focus-visible {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}

button.flex.h-9.items-center.justify-between.whitespace-nowrap.bg-transparent.text-sm.shadow-sm.ring-offset-background.placeholder\:text-muted-foreground.focus\:outline-none.focus\:ring-1.focus\:ring-ring.disabled\:cursor-not-allowed.disabled\:opacity-50.\[\&\>span\]\:line-clamp-1.w-full.rounded-full.border.border-gray-300.p-2.px-4
  span {
  width: 16rem;
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
