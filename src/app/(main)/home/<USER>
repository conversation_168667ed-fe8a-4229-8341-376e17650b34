"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { usePermission } from "@/hooks/usePermission";

export default function Home() {
  const router = useRouter();
  const { role } = usePermission();

  useEffect(() => {
    if (role) {
      switch (role) {
        case 'admin':
          router.push("/home/<USER>");
          break;
        case 'penyuluh':
          router.push("/home/<USER>");
          break;
        case 'distributor':
          router.push("/home/<USER>");
          break;
        case 'user':
          router.push("/home/<USER>");
          break;
        default:
          router.push("/auth/login");
      }
    } else {
      router.push("/auth/login");
    }
  }, [router, role]);

  return (
    <div>HOME</div>
  );
}
