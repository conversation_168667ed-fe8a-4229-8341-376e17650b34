"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import SubmissionFilterModal from "@/components/ui/home/<USER>/submission/modal/SubmissionFilterModal";
import Search from "@/components/ui/search";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/hooks/useAuth";
import { fetchDistributionData } from "@/lib/distribution/distributionFetching";
import { fetchPoktanData } from "@/lib/master/poktanFecthing";
import { usePermission } from "@/store/usePermission";
import { Distribution } from "@/types/distribution/distribution";
import { addDays } from "date-fns";
import {
  CalendarRange,
  EllipsisVertical,
  <PERSON>,
  Filter,
  Printer,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";

export default function DistributionPage() {
  const { getToken } = useAuth();
  const token = getToken();
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dateParent, setDateParent] = useState<DateRange>({
    from: new Date(),
    to: addDays(new Date(), 7),
  });
  const [items, setItems] = useState<Distribution[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);

  // Page size management
  const { pageSize, currentPage, setCurrentPage, handlePageSizeChange } = usePageSize(10);
  const role = usePermission((state: { role: any }) => state.role);
  const [listTabFilter, setSelectTabFilter] = useState<
    { name: string; select: boolean }[]
  >([
    {
      name: "Semua",
      select: true,
    },
    {
      name: "Menunggu",
      select: false,
    },
    {
      name: "Dijadwalkan",
      select: false,
    },
    {
      name: "Selesai",
      select: false,
    },
  ]);

  const [listDistribution, setListDistribution] = useState<Distribution[]>([]);

  const handleChange = (value: string) => {
    setSearch(value);
  };
  const handleDetail = (slug: string, params?: Object) => {
    router.push("/home/<USER>/" + slug + (params ? `?${new URLSearchParams(params as any)}` : ""));
  };
  const handleFilter = () => {
    setIsModalOpen(true);
  };

  const fetchPage = useCallback(
    async (page: number) => {
      if (loading) return;

      setLoading(true);
      const data = await fetchDistributionData(page, String(token));
      setItems(data.items);
      setListDistribution(data.items);
      setTotalPages(data.current_page);
      setTotalItems(data.items.length * data.current_page); // Estimate total items
      setLoading(false);
    },
    [loading, token]
  );

  useEffect(() => {
    fetchPage(currentPage);
  }, [currentPage]);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      {/* header */}
      <div className="flex justify-between items-center">
        <div className="text-lg font-semibold w-full">Data Distribusi</div>
        <DatePickerWithRange date={dateParent} onSelect={setDateParent} />
      </div>

      <div className="mt-4 flex items-center justify-between w-full gap-4">
        <div className="flex items-center w-full gap-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => handleFilter()}
              className="border border-neutral-70 text-neutral-70 px-5 py-5 rounded-full"
            >
              <Filter className="mr-2 text-neutral-70" />
              Filter
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger className="w-10 h-10 flex justify-center items-center rounded-full border border-neutral-70">
                <EllipsisVertical className="cursor-pointer text-neutral-70" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                <DropdownMenuItem className="cursor-pointer">
                  id
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer">
                  Nama
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="w-full">
            <Search value={search} onChange={handleChange} />
          </div>
        </div>
      </div>
      <div className="flex justify-between items-center gap-4 my-4">
        <div className="bg-white rounded-full p-2 shadow-lg">
          {listTabFilter.map((tab, index) => (
            <button
              key={index}
              onClick={() => {
                const updatedTabs = listTabFilter.map((t, i) => ({
                  ...t,
                  select: i === index,
                }));
                setSelectTabFilter(updatedTabs);
              }}
              className={`rounded-full p-2 px-5 transition-all duration-300 ease-in-out ${
                tab.select ? "bg-primary-default text-white" : ""
              }`}
            >
              <span className="text-sm">{tab.name}</span>
            </button>
          ))}
        </div>
        <button className="border border-neutral-70 text-primary-default rounded-full p-2 px-5 flex items-center gap-2">
          <Printer className="h-6 w-6" />
          Print
        </button>
      </div>
      {/* end of header */}

      {/* body */}
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200">No</TableHead>
            <TableHead className="w-[100px] bg-gray-200">Tanggal</TableHead>
            <TableHead className="bg-gray-200">Nama</TableHead>
            <TableHead className="bg-gray-200">Poktan</TableHead>
            <TableHead className="text-center bg-gray-200">
              Jenis Tanaman
            </TableHead>
            <TableHead className="text-center bg-gray-200">
              Jumlah Bibit Yang Diajukan
            </TableHead>
            <TableHead className="text-center bg-gray-200">Status</TableHead>
            <TableHead className="text-right bg-gray-200"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell className="w-[50px]">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
            ))
          ) : listDistribution.length === 0 ? (
            <TableRow>
              <TableCell colSpan={3} className="text-center">
                Tidak ada data tersedia
              </TableCell>
            </TableRow>
          ) : (
            listDistribution.map((value) => (
              <TableRow key={listDistribution.indexOf(value)}>
                <TableCell className="w-[50px]">
                  {listDistribution.indexOf(value) + 1}
                </TableCell>
                <TableCell className="font-medium">
                  {new Date(value.createdAt).toLocaleDateString("id-ID", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                  })}
                </TableCell>
                <TableCell>{value.namaLengkap}</TableCell>
                <TableCell>{value.poktan?.name ?? '-'}</TableCell>
                <TableCell className="text-center">
                  {value.tanaman.name}
                </TableCell>
                <TableCell className="text-center">
                  {value.jumlahTanaman}
                </TableCell>
                <TableCell
                  className={`text-center ${value.status.includes('Selesai') || value.status.includes('Disetujui') ? "text-green-500" : value.status.includes('Dijadwalkan') ? "text-info-500" : value.status.includes('Ditolak') ? "text-danger-600" : value.status.includes('Direvisi') ? "text-warning-600"  : "text-black"}`}
                >
                  {value.status}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger className="border-none bg-transparent active:border-none focus:border-none">
                        <EllipsisVertical className="cursor-pointer" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => {
                            handleDetail("Detail", {id: value.id});
                          }}
                        >
                          <Eye className="mr-2" />
                          Lihat
                        </DropdownMenuItem>
                        {role === "distributor" && (
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => {
                              handleDetail("Jadwalkan", {id: value.id});
                            }}
                          >
                            <CalendarRange className="mr-2" />
                            Jadwalkan Distribusi
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={8}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={handlePageSizeChange}
                isLoading={loading}
                className="mt-4"
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
      {/* component */}
      <SubmissionFilterModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
      />
    </div>
  );
}
