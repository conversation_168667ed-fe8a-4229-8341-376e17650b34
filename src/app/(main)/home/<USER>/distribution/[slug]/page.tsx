"use client";

import { But<PERSON> } from "@/components/ui/button";
import TabDistributionSchedule from "@/components/ui/home/<USER>/distribution/tab-distribution-schedule";
import TabDocumentation from "@/components/ui/home/<USER>/distribution/tab-documentation";
import TabSubmission from "@/components/ui/home/<USER>/distribution/tab-submission";
import { useAuth } from "@/hooks/useAuth";
import { fetchDistributionDataById } from "@/lib/distribution/distributionFetching";
import { getMetodePenanamanById } from "@/lib/master/metodePenanamanFetching";
import { getstatusKepemilikanById } from "@/lib/master/statusKepemilikanFetching";
import { usePermission } from "@/store/usePermission";
import { distribusiTanaman } from "@/types/distribution/distribution";
import { se } from "date-fns/locale";
import { create } from "domain";
import { Printer } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

export default function Page({ params }: { params: { slug: string } }) {
  const [selectTab, setselectTab] = useState(0);
  const role = usePermission((state: { role: any }) => state.role);
  const { getToken } = useAuth();
  const token = getToken();

  // Reset tab to 0 if non-admin tries to access documentation tab
  useEffect(() => {
    if (role !== "admin" && selectTab === 2) {
      setselectTab(0);
    }
  }, [role, selectTab]);
  const [isLoading, setIsLoading] = useState(true);
  const [listDistribution, setListDistribution] = useState<distribusiTanaman[]>([]);
  const [formData, setFormData] = useState({
    namaLengkap: "",
    nik: "",
    noTelepon: "",
    email: "",
    noKartuTani: "",
    noRegistrasiPoktan: "",
    namaKetuaPoktan: "",
    Provinsi: "",
    Kabupaten: "",
    Kecamatan: "",
    DesaKelurahan: "",
    alamat: "",
    luasLahan: 0.0,
    jumlahTanamanHektar: 0,
    masaTanam: "",
    tahunMusimTanam: 0,
    jumlahTanaman: 0,
    alasan: "",
    ktp: "" as File | string,
    kartuTani: "" as File | string,
    tanamanId: "",
    tanamanKebutuhanId: 0,
    statusLahanId: "",
    poktanId: "",
    methodId: "",
    lokasi: "",
    latitude: -6.9175, // Default latitude (e.g., Jakarta)
    longitude: 107.6191, // Default longitude (e.g., Jakarta)
    createdAt: new Date().toISOString(),
  });
  const fetchDataDetail = useCallback(async () => {
    setIsLoading(true);
    const id = Number(new URLSearchParams(window.location.search).get("id"));
    const data = await fetchDistributionDataById(id, String(token));

    const statusLahan = await getstatusKepemilikanById(
      data.statusLahanId ?? 0,
      String(token)
    );
    const metode = await getMetodePenanamanById(
      data.methodId ?? 0,
      String(token)
    );
    setListDistribution(data.distribusiTanaman ?? []);
    setFormData({
      namaLengkap: data.namaLengkap ?? "",
      nik: data.nik,
      noTelepon: data.noTelepon,
      email: data.email,
      noKartuTani: data.noKartuTani ?? "",
      noRegistrasiPoktan: data.noRegistrasiPoktan,
      namaKetuaPoktan: data.namaKetuaPoktan ?? "",
      Provinsi: data.Provinsi,
      Kabupaten: data.Kabupaten,
      Kecamatan: data.Kecamatan,
      DesaKelurahan: data.DesaKelurahan,
      alamat: data.alamat,
      luasLahan: data.luasLahan,
      jumlahTanamanHektar: data.jumlahTanamanHektar,
      masaTanam: data.masaTanam,
      tahunMusimTanam: data.tahunMusimTanam,
      jumlahTanaman: data.jumlahTanaman,
      alasan: data.alasan,
      ktp: data.ktp ?? ("" as File | string),
      kartuTani: data.kartuTani ?? ("" as File | string),
      tanamanId: data.tanaman?.name ?? "",
      tanamanKebutuhanId: Number(data.tanamanKebutuhanId ?? 0),
      statusLahanId: statusLahan.name ?? "",
      poktanId: data.poktan?.name,
      methodId: metode.name ?? "",
      lokasi: "",
      latitude: Number(data.latitude ?? -6.9175), // Default latitude (e.g., Jakarta)
      longitude: Number(data.longitude ?? 107.6191), // Default longitude (e.g., Jakarta)
      createdAt: data.createdAt ?? new Date().toISOString(),
    });
    setIsLoading(false);
    //   setChangeStatus(data.status);
  }, [token]);
  useEffect(() => {
    fetchDataDetail();
  }, [fetchDataDetail, params.slug]);
  return (
    <div className="bg-white p-4 rounded-md shadow-md">
      <div className="flex justify-end items-center mb-4">
        <div className="bg-info-200 px-6 p-2 text-sm rounded-md text-info-500">
          Dijadwalkan
        </div>
      </div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative flex border-b border-gray-300 mb-7 w-full sm:w-96 max-w-md">
          <button
            className={`flex-1 py-2 text-center text-sm font-medium transition-colors duration-200 ${
              selectTab === 0 ? "text-primary-default" : "text-gray-400 hover:text-gray-600"
            }`}
            onClick={() => setselectTab(0)}
          >
            Pengajuan
          </button>
          <button
            className={`flex-1 py-2 text-center text-sm font-medium transition-colors duration-200 ${
              selectTab === 1 ? "text-primary-default" : "text-gray-400 hover:text-gray-600"
            }`}
            onClick={() => setselectTab(1)}
          >
            Jadwal Distribusi
          </button>
          {role === "admin" && (
            <button
              className={`flex-1 py-2 text-center text-sm font-medium transition-colors duration-200 ${
                selectTab === 2 ? "text-primary-default" : "text-gray-400 hover:text-gray-600"
              }`}
              onClick={() => setselectTab(2)}
            >
              Dokumentasi
            </button>
          )}
          <div
            className={`absolute bottom-0 h-1 bg-primary-default transition-all duration-300 ${
              role === "admin"
                ? // Admin: 3 tabs
                  selectTab === 0
                    ? "left-0 w-1/3"
                    : selectTab === 1
                      ? "left-1/3 w-1/3"
                      : "left-2/3 w-1/3"
                : // Non-admin: 2 tabs
                  selectTab === 0
                    ? "left-0 w-1/2"
                    : "left-1/2 w-1/2"
            }`}
          ></div>
        </div>
        <Button className="bg-primary-default hover:bg-primary-600 text-white px-6 py-2 rounded-full flex items-center gap-2 transition-colors duration-200 shadow-sm whitespace-nowrap">
          <Printer className="w-4 h-4" />
          Print
        </Button>
      </div>

      {selectTab === 0 && (
        <TabSubmission
          formData={formData}
          setFormDataChild={(data: any) =>
            setFormData({ ...formData, ...data })
          }
          isLoading={isLoading}
        />
      )}
      {selectTab === 1 && (
        <TabDistributionSchedule
          formData={{
            tanggalDistribusi: new Date(formData.createdAt).toLocaleDateString(
              "id-ID",
              {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              }
            ),
            metodeDistribusi: formData.methodId,
            namaPetugas: formData.namaKetuaPoktan,
            noTeleponPetugas: formData.noTelepon,
            jenisTanaman: formData.tanamanId,
            jumlahTanaman: String(formData.jumlahTanaman),
          }}
          slug={params.slug}
          distribusiTanaman={listDistribution}
        />
      )}
      {selectTab === 2 && <TabDocumentation />}
    </div>
  );
}
