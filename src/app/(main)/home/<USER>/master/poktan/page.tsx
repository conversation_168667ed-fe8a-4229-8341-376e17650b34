"use client";

import { GetServerSideProps } from 'next';
import Search from "@/components/ui/search";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  EllipsisVertical,
  Plus,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { deletePoktanData, fetchPoktanData, searchPoktanData } from '@/lib/master/poktanFecthing';
import { Poktan } from '@/types/master/poktan';
import { usePaginationStore } from '@/store/usePaginationStore';
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";
import { useAuth } from '@/hooks/useAuth';
import { se } from 'date-fns/locale';
import ConfirmasiDeleteModal from '@/components/ui/home/<USER>/master/modal/ConfirmasiDeleteModal';
import { Bounce, toast } from 'react-toastify';

/**
 * PoktanPage component renders a page for managing "Kelompok Tani" (Poktan) groups.
 *
 * @component
 * @example
 * return (
 *   <PoktanPage />
 * )
 *
 * @returns {JSX.Element} The rendered PoktanPage component.
 *
 * @description
 * This component includes a search bar, a button to add new Poktan, and a table displaying a list of Poktan groups.
 * Each row in the table includes options to view details or edit the Poktan group.
 *
 * @function
 * @name PoktanPage
 *
 * @property {string} search - The search term used to filter the list of Poktan groups.
 * @property {function} setSearch - Function to update the search term.
 * @property {Array<Object>} listPoktan - The list of Poktan groups.
 * @property {function} setListPoktan - Function to update the list of Poktan groups.
 *
 * @param {string} listPoktan[].name - The name of the Poktan group.
 *
 * @property {function} handleChange - Function to handle changes in the search input.
 * @property {function} handleSlugPoktan - Function to navigate to a specific Poktan page based on the slug.
 *
 * @requires useRouter from 'next/navigation'
 * @requires useState, useEffect from 'react'
 * @requires Search from '@/components/ui/search'
 * @requires Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow from '@/components/ui/table'
 * @requires DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger from '@/components/ui/dropdown-menu'
 * @requires ChevronDown, ChevronLeft, ChevronRight, EllipsisVertical, Plus from 'lucide-react'
 */
export default function PoktanPage() {
  const { getToken } = useAuth();
  const token = getToken();
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [listPoktan, setListPoktan] = useState<Poktan[]>([]);
  const [items, setItems] = useState<Poktan[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState("");
  const [id, setId] = useState(0);

  // Page size management
  const { pageSize, currentPage, setCurrentPage, handlePageSizeChange } = usePageSize(10);

  const handleChange = (value: string) => {
    setSearch(value);
    if (value.length > 0) {
      handleSearchPoktan(value);
    } else {
      fetchPage(1);
    }
  };

  const handleSlugPoktan = (slug: string, params?: Object) => {
    router.push("/home/<USER>/poktan/" + slug + (params ? `?${new URLSearchParams(params as any)}` : ""));
  };

  const handleSearchPoktan = async (search: string) => {
    setLoading(true);
    const data = await searchPoktanData(search, String(token));
    setItems(data.items);
    setListPoktan(data.items);
    setTotalPages(data.current_page);
    setLoading(false);
  };

  const handleDeletePoktan = async () => {
    setLoading(true);
    setIsOpen(false);
    await deletePoktanData(id, String(token)).then((response) => {
      if (response.ok) {
        fetchPage(1);
        toast.success('Data berhasil dihapus', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      } else {
        toast.error('Data gagal dihapus', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      }
    }).catch((error) => {
      console.error("Error deleting Poktan:", error);
    }
    );

    setLoading(false);
  }

  const handleOpenModal = (id: number) => {
    setIsOpen(true);
    setStatus("Kelompok Tani");
    setId(id);
  };

  const fetchPage = useCallback(async (page: number) => {
    if (loading) return;

    setLoading(true);
    const data = await fetchPoktanData(page, String(token));
    setItems(data.items);
    setListPoktan(data.items);
    setTotalPages(data.current_page);
    setTotalItems(data.items.length * data.current_page); // Estimate total items
    setLoading(false);
  }, [loading, token]);

  useEffect(() => {
      fetchPage(currentPage);
  }, [currentPage]);

  return (
    <div className="bg-white p-4 rounded-md shadow-md font-poppins">
      <div className="text-lg font-medium mb-4">
        Nama Kelompok Tani (Poktan)
      </div>
      <div className="flex justify-between items-center gap-4 mb-4">
        <div>
          <button
            onClick={() => handleSlugPoktan("Tambah Poktan")}
            className="bg-primary-500 flex text-white px-5 py-2 text-nowrap rounded-full"
          >
            <Plus className="mr-2" />
            Tambah
          </button>
        </div>
        <div className="w-full">
          <Search value={search} onChange={handleChange} />
        </div>
      </div>
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200">No</TableHead>
            <TableHead className="bg-gray-200">Nama Kelompok Poktan</TableHead>
            <TableHead className="text-right bg-gray-200"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell className="w-[50px]">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
            ))
          ) : listPoktan.length === 0 ? (
            <TableRow>
              <TableCell colSpan={3} className="text-center">
                Tidak ada data tersedia
              </TableCell>
            </TableRow>
          ) : (
            listPoktan.map((value) => (
              <TableRow key={listPoktan.indexOf(value)}>
                <TableCell className="w-[50px]">
                  {listPoktan.indexOf(value) + 1}
                </TableCell>
                <TableCell className="font-medium">{value.name}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger className="border-none bg-transparent active:border-none focus:border-none">
                        <EllipsisVertical className="cursor-pointer" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleSlugPoktan("Detail Poktan", { id: value.id })}
                        >
                          Detail
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleSlugPoktan("Edit Poktan", { id: value.id })}
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => handleOpenModal(value.id)}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={3}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={handlePageSizeChange}
                isLoading={loading}
                className="mt-4"
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>

      {/* Modal */}
      <ConfirmasiDeleteModal isOpen={isOpen} onBatal={() => { setIsOpen(false) }} onClose={() => { setIsOpen(false); }} onSimpan={handleDeletePoktan} status={status} />

    </div>
  );
}
