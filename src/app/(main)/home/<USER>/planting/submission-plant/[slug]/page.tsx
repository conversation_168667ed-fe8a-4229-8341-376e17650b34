"use client";

import FormInput from "@/components/ui/base/form-input";
import FormLabel from "@/components/ui/base/form-label";
import FormSelect from "@/components/ui/base/form-select";
import FormTextArea from "@/components/ui/base/form-text-area";
import { Button } from "@/components/ui/button";
import DatePicker from "@/components/ui/date-picker";
import PlantingStatusModal from "@/components/ui/home/<USER>/planting/PlantingStatusModal";
import PlantingSubmissionModal from "@/components/ui/home/<USER>/planting/PlantingSubmissionModal";
import { useAuth } from "@/hooks/useAuth";
import { fetchPenggunaById } from "@/lib/management-user/penggunaFetching";
import { fetchJenisTanamanData } from "@/lib/master/jenisTanamanFetching";
import {
  fetchSubmissionPlantDataById,
  postSubmissionPlantData,
} from "@/lib/planting/submissionPlantFetching";
import { getUser } from "@/store/auth/userStore";
import { usePermission } from "@/store/usePermission";
import { JenisTanaman } from "@/types/master/jenisTanaman";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Bounce, toast } from "react-toastify";

export default function Component({ params }: { params: { slug: string } }) {
  const { getToken } = useAuth();
  const token = getToken();
  const user = getUser();
  const role = usePermission((state) => state.role);
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenReject, setIsOpenReject] = useState(false);
  const [listPlant, setListPlant] = useState<JenisTanaman[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState({
    name: "",
  });
  const [formData, setFormData] = useState({
    tanggalPengajuan: new Date(),
    tanamanId: 0,
    jumlah: 0,
    tanggalKebutuhan: new Date(),
    alasan: "",
  });
  const [messageError, setMessageError] = useState<
    Record<keyof typeof formData, string | null>
  >({
    tanggalPengajuan: null,
    tanamanId: null,
    jumlah: null,
    tanggalKebutuhan: null,
    alasan: null,
  });

  const clearFormData = () => {
    setFormData({
      tanggalPengajuan: new Date(),
      tanamanId: 0,
      jumlah: 0,
      tanggalKebutuhan: new Date(),
      alasan: "",
    });
    setMessageError({
      tanggalPengajuan: null,
      tanamanId: null,
      jumlah: null,
      tanggalKebutuhan: null,
      alasan: null,
    });
  };

  const fetchListPlant = async () => {
    const data = await fetchJenisTanamanData(1, String(token));
    setListPlant(data.items);
    if (!params.slug.includes("Tambah")) {
      const id = Number(new URLSearchParams(window.location.search).get("id"));

      var res = await fetchSubmissionPlantDataById(String(id), String(token));
      if (
        res &&
        res.tanggalPengajuan &&
        res.tanggalKebutuhan &&
        res.tanamanId !== undefined &&
        res.jumlah !== undefined
      ) {
        const data = await fetchPenggunaById(Number(res.userId), String(token));
        setUserData({
          name: data.name || "",
        });
        setFormData({
          tanggalPengajuan: new Date(res.tanggalPengajuan),
          tanamanId: res.tanamanId,
          jumlah: res.jumlah,
          tanggalKebutuhan: new Date(res.tanggalKebutuhan),
          alasan: res.alasan || "",
        });
      } else {
        toast.error("Data pengajuan tidak ditemukan atau tidak lengkap.", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      }
    }
  };

  const handleAjukan = async () => {
    setMessageError({
      tanggalPengajuan: null,
      tanamanId: null,
      jumlah: null,
      tanggalKebutuhan: null,
      alasan: null,
    });

    if (params.slug.includes("Tambah")) {
      await postSubmissionPlantData(formData, String(token))
        .then((response) => {
          if (!response.ok) {
            response.json().then((errorData) => {
              setMessageError(errorData.data);
            });

            throw new Error("Failed to save data");
          }
          return response.json();
        })
        .then((data) => {
          toast.success("Data berhasil disimpan", {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });

          clearFormData();
          setIsLoading(false);
          router.push("/home/<USER>/submission-plant");
        })
        .catch((error) => {
          setIsLoading(false);
          console.error("Error:", error);
          toast.error(`${error}`, {
            position: "top-right",
            autoClose: 5000,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
            transition: Bounce,
          });
        });
    }
  };

  useEffect(() => {
    fetchListPlant();
  }, []);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      {/* header */}
      <div className="flex justify-end items-center mb-4">
        <div className="bg-gray-400 rounded-md text-sm p-2 px-8">Diajukan</div>
      </div>
      {/* end header */}
      {/* body */}
      {params.slug.includes("Detail") ? (
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormLabel label="Distributor" value={String(userData.name)} />
            <FormLabel
              label="Tanggal Pengajuan"
              value={formData.tanggalPengajuan.toLocaleDateString("id-ID", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              })}
              required
            />
            <FormLabel
              label="Jenis Tanaman"
              value={
                listPlant.find((plant) => plant.id === formData.tanamanId)
                  ?.name || ""
              }
              required
            />
            <FormLabel
              label="Jumlah Tanaman yang diajukan"
              value={String(formData.jumlah)}
              required
            />
            <FormLabel
              label="Tanggal Kebutuhan Tanaman"
              value={formData.tanggalKebutuhan.toLocaleDateString("id-ID", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              })}
              required
            />
            <FormLabel
              label="Alasan  Pengajuan (Optional)"
              value={formData.alasan || "Tidak ada alasan"}
            />
          </div>
          {(role === "admin" && (
            <div className="flex justify-end gap-4">
              <Button
                onClick={() => {
                  setIsOpen(true);
                }}
                className="bg-success-600 text-white px-8 tex-sm rounded-full"
              >
                Disetujui
              </Button>
              <Button
                onClick={() => {
                  setIsOpenReject(true);
                }}
                className="bg-error-500 text-white px-8 tex-sm rounded-full"
              >
                Ditolak
              </Button>
            </div>
          )) || (
            <div className="flex justify-end gap-4">
              <Button
                onClick={() => {
                  router.back();
                }}
                className="border border-primary-default text-primary-default px-8 tex-sm rounded-full"
              >
                Kembali
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DatePicker
              label="Tanggal Pengajuan"
              date={formData.tanggalPengajuan}
              onSelect={(date: Date) =>
                setFormData({ ...formData, tanggalPengajuan: date })
              }
              errorMessage={messageError.tanggalPengajuan}
            />
            <FormSelect
              label="Jenis Tanaman"
              value={listPlant.map((value) => value.name)}
              selected={
                listPlant.find((plant) => plant.id === formData.tanamanId)
                  ?.name || ""
              }
              onChange={(value: string) => {
                const selectedPlant = listPlant.find(
                  (plant) => plant.name === value
                );
                if (selectedPlant) {
                  setFormData({
                    ...formData,
                    tanamanId: selectedPlant.id,
                  });
                }
              }}
              errorMessage={messageError.tanamanId}
              required
            />
            <FormInput
              label="Jumlah Tanaman "
              placeholder="Masukan Jumlah Tanaman "
              value={String(formData.jumlah)}
              onChange={(value: string) =>
                setFormData({
                  ...formData,
                  jumlah: Number(value),
                })
              }
              errorMessage={messageError?.jumlah}
              required
              type="number"
            />
            <DatePicker
              label="Tanggal Kebutuhan Tanaman"
              date={formData.tanggalKebutuhan}
              onSelect={(date: Date) =>
                setFormData({ ...formData, tanggalKebutuhan: date })
              }
              errorMessage={messageError.tanggalKebutuhan}
            />
          </div>
          <FormTextArea
            label="Alasan"
            placeholder="Masukan Alasan"
            value={formData.alasan}
            onChange={(value: string) =>
              setFormData({ ...formData, alasan: value })
            }
            errorMessage={messageError.alasan}
            required
          />
          <div className="flex justify-end gap-4">
            <Button
              onClick={() => {
                router.back();
              }}
              className="border border-primary-default text-primary-default px-8 tex-sm rounded-full"
            >
              Kembali
            </Button>
            <Button
              onClick={() => {
                handleAjukan();
              }}
              className="bg-primary-default text-white px-8 tex-sm rounded-full"
            >
              Ajukan
            </Button>
          </div>
        </div>
      )}
      {/* end body */}
      {/* Component */}
      <PlantingSubmissionModal
        isOpen={isOpen}
        onClose={() => {
          setIsOpen(false);
        }}
      />
      <PlantingStatusModal
        isOpen={isOpenReject}
        onClose={() => {
          setIsOpenReject(false);
        }}
      />
      {/* End Component */}
    </div>
  );
}
