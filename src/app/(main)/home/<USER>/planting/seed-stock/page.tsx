"use client";
import { useRouter } from "next/navigation";
import { usePermission } from "@/hooks/usePermission";
import { useCallback, useEffect, useState } from "react";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Button } from "@/components/ui/button";
import {
  CalendarRange,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  EllipsisVertical,
  Eye,
  Filter,
  Plus,
  Printer,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import SubmissionFilterModal from "@/components/ui/home/<USER>/submission/modal/SubmissionFilterModal";
import {
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  TableFooter,
  Table,
} from "@/components/ui/table";
import Search from "@/components/ui/search";
import { useAuth } from "@/hooks/useAuth";
import { SeedStock } from "@/types/planting/seedStock";
import { fetchSeedStockData } from "@/lib/planting/seedStockFetching";
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";

export default function SeedStokPage() {
  const { getToken } = useAuth();
  const token = getToken();
  const router = useRouter();
  const { pageSize } = usePageSize();
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dateParent, setDateParent] = useState<DateRange>({
    from: new Date(),
    to: addDays(new Date(), 7),
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);

  const [listStokBibit, setlistStokBibit] = useState<SeedStock[]>([]);

  const handlePageSizeChange = (newPageSize: number) => {
    setCurrentPage(1); // Reset to first page when page size changes
    // The pageSize will be updated by the usePageSize hook
  };

  const handleChange = (value: string) => {
    setSearch(value);
  };
  const handleDetail = (slug: string, params?: Object) => {
    router.push(
      "/home/<USER>/seed-stock/" +
        slug +
        (params ? `?${new URLSearchParams(params as any)}` : "")
    );
  };
  const handleFilter = () => {
    setIsModalOpen(true);
  };
  const fetchPage = useCallback(
    async (page: number) => {
      if (loading) return;

      setLoading(true);
      try {
        const data = await fetchSeedStockData(page, String(token));
        setlistStokBibit(data.items || []);
        setTotalPages(data.total_pages || 0);
        setTotalItems(data.total_items || 0);
      } catch (error) {
        console.error("Error fetching seed stock data:", error);
        setlistStokBibit([]);
        setTotalPages(0);
        setTotalItems(0);
      } finally {
        setLoading(false);
      }
    },
    [loading, token]
  );

  useEffect(() => {
    fetchPage(currentPage);
  }, [currentPage, pageSize]);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      {/* header */}
      <div className="flex justify-between items-center">
        <div className="text-lg font-semibold w-full">Data Stok Bibit Tanaman</div>
        <DatePickerWithRange date={dateParent} onSelect={setDateParent} />
      </div>

      <div className="mt-4 flex items-center justify-between w-full gap-4">
        <div className="flex items-center w-full gap-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => handleFilter()}
              className="border border-neutral-70 text-neutral-70 px-5 py-5 rounded-full"
            >
              <Filter className="mr-2 text-neutral-70" />
              Filter
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger className="w-10 h-10 flex justify-center items-center rounded-full border border-neutral-70">
                <EllipsisVertical className="cursor-pointer text-neutral-70" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                <DropdownMenuItem className="cursor-pointer">
                  id
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer">
                  Nama
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="w-full">
            <Search value={search} onChange={handleChange} />
          </div>
          <div className="flex items-center gap-4">
            <Button className="border border-neutral-70 text-primary-default rounded-full p-2 px-5 flex items-center gap-2">
              <Printer className="h-6 w-6" />
              Print
            </Button>
            <Button
              onClick={() => {
                handleDetail("Tambah Stok Bibit");
              }}
              className="bg-primary-default flex items-center gap-1 rounded-full text-white"
            >
              <Plus className="mr-2" />
              Tambah
            </Button>
          </div>
        </div>
      </div>
      {/* end of header */}

      {/* body */}
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200">No</TableHead>
            <TableHead className="text-left bg-gray-200">
              Jenis Tanaman
            </TableHead>
            <TableHead className="text-center bg-gray-200">
              Jumlah Bibit Yang Diajukan
            </TableHead>
            <TableHead className="text-right bg-gray-200"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell className="w-[50px]">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                </TableCell>
              </TableRow>
            ))
          ) : listStokBibit.length === 0 ? (
            <TableRow>
              <TableCell colSpan={3} className="text-center">
                Tidak ada data tersedia
              </TableCell>
            </TableRow>
          ) : (
            listStokBibit.map((value) => (
              <TableRow key={listStokBibit.indexOf(value)}>
                <TableCell className="w-[50px]">
                  {listStokBibit.indexOf(value) + 1}
                </TableCell>
                <TableCell className="text-left">{value.plant.name}</TableCell>
                <TableCell className="text-center">{value.jumlah}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger className="border-none bg-transparent active:border-none focus:border-none">
                        <EllipsisVertical className="cursor-pointer" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={() => {
                            handleDetail("Detail Stok Bibit", {
                              id: value.id,
                              jenisTanaman: value.plant.name,
                              jumlah: value.jumlah,
                            });
                          }}
                        >
                          <Eye className="mr-2" />
                          Detail
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={4}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={handlePageSizeChange}
                isLoading={loading}
                className="mt-4"
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
      {/* component */}
      <SubmissionFilterModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
      />
    </div>
  );
}
