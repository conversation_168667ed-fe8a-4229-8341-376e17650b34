"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Home, ArrowLeft, Search } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function NotFound() {
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/home');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="relative">
            <h1 className="text-9xl font-bold text-gray-200 select-none">
              404
            </h1>
            <div className="absolute inset-0 flex items-center justify-center">
              <Search className="w-16 h-16 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Halaman Tidak Ditemukan
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed">
            Maaf, halaman yang Anda cari tidak dapat ditemukan. 
            Mungkin halaman telah dipindahkan, dihapus, atau URL yang Anda masukkan salah.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={handleGoHome}
              className="bg-primary-default hover:bg-primary-600 text-white px-6 py-3 flex items-center justify-center gap-2"
            >
              <Home className="w-5 h-5" />
              Kembali ke Beranda
            </Button>
            
            <Button 
              onClick={handleGoBack}
              variant="outline"
              className="px-6 py-3 flex items-center justify-center gap-2"
            >
              <ArrowLeft className="w-5 h-5" />
              Kembali
            </Button>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-4">
            Atau coba kunjungi halaman berikut:
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/home" 
              className="text-primary-default hover:text-primary-600 hover:underline"
            >
              Dashboard
            </Link>
            <Link 
              href="/home/<USER>" 
              className="text-primary-default hover:text-primary-600 hover:underline"
            >
              Profil
            </Link>
            <Link 
              href="/home/<USER>" 
              className="text-primary-default hover:text-primary-600 hover:underline"
            >
              Pengajuan
            </Link>
            <Link 
              href="/login" 
              className="text-primary-default hover:text-primary-600 hover:underline"
            >
              Login
            </Link>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-8 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-2">
            Butuh Bantuan?
          </h3>
          <p className="text-sm text-gray-600">
            Jika Anda yakin halaman ini seharusnya ada, silakan hubungi tim support 
            atau laporkan masalah ini kepada administrator.
          </p>
        </div>
      </div>
    </div>
  );
}
