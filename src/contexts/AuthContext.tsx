"use client"
import React, { createContext, useState, useContext, ReactNode, useCallback, useMemo, useEffect } from 'react';

// Define tipe untuk context
interface AuthContextType {
    token: string | null;
    setAuthToken: (token: string | null) => void;
    getToken: () => string | null;
    isLoading: boolean;
}

// Default nilai untuk context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider untuk menyertakan AuthContext di aplikasi
interface AuthProviderProps {
    children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [token, setToken] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Initialize token from localStorage on mount
    useEffect(() => {
        if (typeof window !== 'undefined') {
            const storedToken = localStorage.getItem('authToken');
            if (storedToken) {
                setToken(storedToken);
            }
            setIsLoading(false);
        }
    }, []);

    // Memoized function untuk mengatur token
    const setAuthToken = useCallback((newToken: string | null) => {
        setToken(newToken);
        if (typeof window !== 'undefined') {
            if (newToken) {
                localStorage.setItem('authToken', newToken);
            } else {
                localStorage.removeItem('authToken');
            }
        }
    }, []);

    // Memoized function untuk mengambil token
    const getToken = useCallback((): string | null => {
        if (typeof window !== 'undefined') {
            return localStorage.getItem('authToken');
        }
        return token;
    }, [token]);

    // Memoize context value to prevent unnecessary re-renders
    const contextValue = useMemo(() => ({
        token,
        setAuthToken,
        getToken,
        isLoading
    }), [token, setAuthToken, getToken, isLoading]);

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook untuk menggunakan AuthContext
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};