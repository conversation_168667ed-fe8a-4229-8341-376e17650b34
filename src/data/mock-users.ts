export const mockUsers = [
    {
        id: '1',
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Admin User',
        role: 'admin',
        token: 'admin-mock-token-123'
    },
    {
        id: '2',
        email: '<EMAIL>',
        password: 'penyuluh123',
        name: 'Penyuluh User',
        role: 'penyuluh',
        token: 'penyuluh-mock-token-123'
    },
    {
        id: '3',
        email: '<EMAIL>',
        password: 'distributor123',
        name: 'Distributor User',
        role: 'distributor',
        token: 'distributor-mock-token-123'
    },
    {
        id: '4',
        email: '<EMAIL>',
        password: 'user123',
        name: 'Regular User',
        role: 'user',
        token: 'user-mock-token-123'
    }
]

export const findUser = (email: string, password: string) => {
    return mockUsers.find(user =>
        user.email === email && user.password === password
    )
}