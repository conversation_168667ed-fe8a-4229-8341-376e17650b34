import { ApiResponse, ApiResponseId } from "@/types/master/metodePenanaman";

// Fungsi untuk mengambil data dari API
export const fetchMetodePenanamanData = async (page: number, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk?page=${page}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

// Fungsi untuk menambahkan data jenis tanaman
export const postMetodePenanamanData = async (name: string, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ name: name }),
    });
    const data = await res;
    return data;
};

// Fungsi untuk mengupdate data jenis tanaman
export const putMetodePenanamanData = async (id: number, name: string, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk/${id}`, {
        method: "PUT",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ name: name }),
    });
    const data = await res;
    return data;
};

// Fungsi untuk menghapus data jenis tanaman
export const deleteMetodePenanamanData = async (id: number, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk/${id}`, {
        method: "DELETE",
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res;
    return data;
};

// Fungsi untuk mengambil data jenis tanaman berdasarkan ID
export const getMetodePenanamanById = async (id: number, token: string): Promise<ApiResponseId['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk/${id}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
}

// Fungsi untuk search data jenis tanaman
export const searchMetodePenanamanData = async (search: string, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}master/method-pupuk?search=${search}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};