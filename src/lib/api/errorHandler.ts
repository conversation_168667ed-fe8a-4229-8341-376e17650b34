/**
 * API Error Handler
 * Handles various types of API errors and provides appropriate responses
 */

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class NetworkError extends Error {
  status?: number;
  code?: string;
  details?: any;

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message);
    this.name = 'NetworkError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

/**
 * Check if the API URL is accessible
 */
export async function checkApiHealth(): Promise<boolean> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      console.error('API URL not configured');
      return false;
    }

    // Simple health check - try to reach the API
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`${apiUrl}health`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
}

/**
 * Enhanced fetch with error handling
 */
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  
  if (!apiUrl) {
    throw new NetworkError('API URL not configured', 500, 'CONFIG_ERROR');
  }

  const url = `${apiUrl}${endpoint}`;
  
  // Default options
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(url, {
      ...defaultOptions,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // Handle different HTTP status codes
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
        errorDetails = errorData;
      } catch {
        // If response is not JSON, use status text
      }

      throw new NetworkError(
        errorMessage,
        response.status,
        `HTTP_${response.status}`,
        errorDetails
      );
    }

    // Parse JSON response
    const data = await response.json();
    return data;

  } catch (error) {
    // Handle different types of errors
    if (error instanceof NetworkError) {
      throw error;
    }

    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new NetworkError(
        'Network connection failed. Please check your internet connection and try again.',
        0,
        'NETWORK_ERROR',
        { originalError: error.message }
      );
    }

    if (error instanceof DOMException && error.name === 'AbortError') {
      throw new NetworkError(
        'Request timeout. The server is taking too long to respond.',
        408,
        'TIMEOUT_ERROR'
      );
    }

    // Generic error
    throw new NetworkError(
      `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      'UNKNOWN_ERROR',
      { originalError: error }
    );
  }
}

/**
 * Retry mechanism for failed requests
 */
export async function apiRequestWithRetry<T>(
  endpoint: string,
  options: RequestInit = {},
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> {
  let lastError: NetworkError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiRequest<T>(endpoint, options);
    } catch (error) {
      lastError = error instanceof NetworkError ? error : new NetworkError(
        error instanceof Error ? error.message : 'Unknown error'
      );

      console.warn(`API request attempt ${attempt} failed:`, lastError.message);

      // Don't retry on certain errors
      if (lastError.status === 401 || lastError.status === 403 || lastError.status === 404) {
        throw lastError;
      }

      // Wait before retrying (except on last attempt)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
  }

  throw lastError!;
}

/**
 * Get user-friendly error message
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof NetworkError) {
    switch (error.code) {
      case 'NETWORK_ERROR':
        return 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
      case 'TIMEOUT_ERROR':
        return 'Server membutuhkan waktu terlalu lama untuk merespons. Coba lagi nanti.';
      case 'HTTP_401':
        return 'Sesi Anda telah berakhir. Silakan login kembali.';
      case 'HTTP_403':
        return 'Anda tidak memiliki izin untuk mengakses resource ini.';
      case 'HTTP_404':
        return 'Data yang diminta tidak ditemukan.';
      case 'HTTP_500':
        return 'Terjadi kesalahan pada server. Coba lagi nanti.';
      default:
        return error.message;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui.';
}
