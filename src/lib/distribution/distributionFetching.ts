import { ApiResponse, ApiResponseById } from "@/types/distribution/distribution";

// Fungsi untuk mengambil data dari API
export const fetchDistributionData = async (page: number, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman?page=${page}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

export const fetchDistributionDataById = async (id: number, token: string): Promise<ApiResponseById['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman/${id}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
};

// Fungsi untuk mengirim data ke API
export const postDistributionScheduleData = async (payload: object, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman/schedule`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
};

export const postDistributionDocumentData = async (payload: any, token: string): Promise<Response> => {
    
    const formData = new FormData();
    // Assume payload is an object with key-value pairs, where value can be File or primitive
    Object.entries(payload).forEach(([key, value]) => {
        if (value instanceof File || value instanceof Blob) {
            formData.append(key, value);
        } else {
            formData.append(key, value as string);
        }
    });

    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman/documentation`, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            // Do not set 'Content-Type', let browser set it for FormData
        },
        body: formData,
    });
    return res;
};

// Fungsi untuk mengupdate data ke API
export const putDistributionData = async (id: number, payload: object, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
    });
    const data = await res;
    return data;
};

// Fungsi untuk menghapus data dari API
export const deleteDistributionData = async (id: number, token: string): Promise<Response> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman/${id}`, {
        method: 'DELETE',
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res;
    return data;
};
// Fungsi untuk mencari data poktan berdasarkan nama
export const searchDistributionData = async (search: string, token: string): Promise<ApiResponse['data']> => {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}distributions/tanaman?search=${search}`, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    const data = await res.json();
    return data.data;
}