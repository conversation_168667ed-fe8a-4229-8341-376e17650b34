/**
 * Environment utilities for checking API connectivity and configuration
 */

export interface EnvironmentCheck {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Check if all required environment variables are set
 */
export function checkEnvironmentVariables(): EnvironmentCheck {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required environment variables
  const requiredVars = [
    'NEXT_PUBLIC_API_URL',
  ];

  // Optional but recommended environment variables
  const optionalVars = [
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_VERSION',
  ];

  // Check required variables
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      errors.push(`Missing required environment variable: ${varName}`);
    } else if (varName === 'NEXT_PUBLIC_API_URL') {
      // Validate API URL format
      try {
        new URL(value);
      } catch {
        errors.push(`Invalid API URL format: ${varName}`);
      }
    }
  });

  // Check optional variables
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      warnings.push(`Missing optional environment variable: ${varName}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get API base URL with validation
 */
export function getApiUrl(): string {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  
  if (!apiUrl) {
    throw new Error('API URL not configured. Please set NEXT_PUBLIC_API_URL environment variable.');
  }

  try {
    new URL(apiUrl);
    return apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
  } catch {
    throw new Error(`Invalid API URL format: ${apiUrl}`);
  }
}

/**
 * Check if we're in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Check if we're in production mode
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Get app configuration
 */
export function getAppConfig() {
  return {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'SimTanaman',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    apiUrl: getApiUrl(),
    isDevelopment: isDevelopment(),
    isProduction: isProduction(),
  };
}

/**
 * Check network connectivity
 */
export async function checkNetworkConnectivity(): Promise<boolean> {
  try {
    // Try to fetch a simple resource
    const response = await fetch('/api/health', {
      method: 'HEAD',
      cache: 'no-cache',
    });
    return response.ok;
  } catch {
    // If local health check fails, try external
    try {
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache',
      });
      return true; // If we reach here, network is working
    } catch {
      return false;
    }
  }
}

/**
 * Comprehensive environment and connectivity check
 */
export async function performSystemCheck(): Promise<{
  environment: EnvironmentCheck;
  network: boolean;
  api: boolean;
  overall: boolean;
}> {
  const environment = checkEnvironmentVariables();
  const network = await checkNetworkConnectivity();
  
  let api = false;
  if (environment.isValid && network) {
    try {
      const apiUrl = getApiUrl();
      const response = await fetch(`${apiUrl}/health`, {
        method: 'HEAD',
        cache: 'no-cache',
      });
      api = response.ok;
    } catch {
      api = false;
    }
  }

  return {
    environment,
    network,
    api,
    overall: environment.isValid && network && api
  };
}
