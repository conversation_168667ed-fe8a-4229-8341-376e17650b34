import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { NetworkError, getErrorMessage } from '@/lib/api/errorHandler';

interface UseApiErrorOptions {
  redirectOnUnauthorized?: boolean;
  showToast?: boolean;
  customErrorHandler?: (error: NetworkError) => void;
}

export function useApiError(options: UseApiErrorOptions = {}) {
  const {
    redirectOnUnauthorized = true,
    showToast = true,
    customErrorHandler
  } = options;

  const router = useRouter();
  const [error, setError] = useState<NetworkError | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleError = useCallback((error: unknown) => {
    const networkError = error instanceof NetworkError
      ? error
      : new NetworkError(
          error instanceof Error ? error.message : 'Unknown error'
        );

    setError(networkError);

    // Handle specific error types
    if (networkError.status === 401 && redirectOnUnauthorized) {
      if (showToast) {
        toast.error('Sesi Anda telah berakhir. Silakan login kembali.');
      }
      router.push('/login');
      return;
    }

    if (networkError.status === 404) {
      router.push('/404');
      return;
    }

    // Custom error handler
    if (customErrorHandler) {
      customErrorHandler(networkError);
      return;
    }

    // Show toast notification
    if (showToast) {
      const message = getErrorMessage(networkError);
      toast.error(message);
    }

    console.error('API Error:', networkError);
  }, [redirectOnUnauthorized, showToast, customErrorHandler, router]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const executeWithErrorHandling = useCallback(async <T>(
    apiCall: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await apiCall();
      return result;
    } catch (error) {
      handleError(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  return {
    error,
    isLoading,
    handleError,
    clearError,
    executeWithErrorHandling
  };
}

// Hook for specific API operations
export function useApiOperation<T>() {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (
    apiCall: () => Promise<T>,
    options: {
      onSuccess?: (data: T) => void;
      onError?: (error: string) => void;
      showSuccessToast?: boolean;
      successMessage?: string;
    } = {}
  ) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await apiCall();
      setData(result);
      
      if (options.onSuccess) {
        options.onSuccess(result);
      }
      
      if (options.showSuccessToast && options.successMessage) {
        toast.success(options.successMessage);
      }
      
      return result;
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);

      if (options.onError) {
        options.onError(errorMessage);
      } else {
        toast.error(errorMessage);
      }

      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    data,
    isLoading,
    error,
    execute,
    reset
  };
}

// Hook for data fetching with retry
export function useApiData<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await apiCall();
      setData(result);
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Data fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  const retry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    fetchData();
  }, [fetchData]);

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    retry,
    refetch: fetchData
  };
}
