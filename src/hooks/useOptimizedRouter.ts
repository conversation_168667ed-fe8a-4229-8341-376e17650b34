import { useRouter } from 'next/navigation';
import { useCallback, useTransition } from 'react';

/**
 * Hook for optimized router navigation with loading states
 */
export function useOptimizedRouter() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const push = useCallback((href: string) => {
    startTransition(() => {
      router.push(href);
    });
  }, [router]);

  const replace = useCallback((href: string) => {
    startTransition(() => {
      router.replace(href);
    });
  }, [router]);

  const back = useCallback(() => {
    startTransition(() => {
      router.back();
    });
  }, [router]);

  const forward = useCallback(() => {
    startTransition(() => {
      router.forward();
    });
  }, [router]);

  const refresh = useCallback(() => {
    startTransition(() => {
      router.refresh();
    });
  }, [router]);

  return {
    push,
    replace,
    back,
    forward,
    refresh,
    isPending,
  };
}

export default useOptimizedRouter;
