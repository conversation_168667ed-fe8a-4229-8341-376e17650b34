import { useEffect } from 'react';
import { usePermission } from '@/store/usePermission';

/**
 * Hook to initialize role from cookies
 * This ensures role is available throughout the app
 */
export function useRoleInitializer() {
  const { role, setRole } = usePermission();

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      // Get role from cookie
      const roleFromCookie = document.cookie
        .split('; ')
        .find((row) => row.startsWith('role='))
        ?.split('=')[1] || '';

      // Set role if not already set and cookie exists
      if (!role && roleFromCookie) {
        console.log('Setting role from cookie:', roleFromCookie);
        setRole(roleFromCookie);
      }
    }
  }, [role, setRole]);

  return { role };
}

export default useRoleInitializer;
