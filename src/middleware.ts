import { NextResponse } from 'next/server'
import { type NextRequest } from 'next/server'

// Cache public paths for better performance
const PUBLIC_PATHS = new Set(['/auth/login', '/auth/register', '/auth/forgot-password']);

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    const token = request.cookies.get('token')?.value;

    // Create response with security headers
    const response = NextResponse.next();
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'origin-when-cross-origin');

    // Handle public routes
    if (PUBLIC_PATHS.has(pathname)) {
        if (token) {
            return NextResponse.redirect(new URL('/home/<USER>', request.url));
        }
        return response;
    }

    // Redirect to login if no token
    if (!token) {
        const loginUrl = new URL('/auth/login', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        return NextResponse.redirect(loginUrl);
    }

    // Handle root home path with role-based redirection
    if (pathname === '/home') {
        const role = request.cookies.get('role')?.value;

        if (['admin', 'penyuluh', 'distributor', 'user'].includes(role || '')) {
            return NextResponse.redirect(new URL('/home/<USER>', request.url));
        }

        return NextResponse.redirect(new URL('/auth/login', request.url));
    }

    return response;
}

export const config = {
    matcher: [
        '/home/<USER>',
        '/auth/:path*'
    ]
}