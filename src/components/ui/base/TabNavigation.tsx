"use client";

import { ReactNode, useState, useEffect } from "react";

interface Tab {
  id: number;
  label: string;
  visible?: boolean;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: number;
  onTabChange: (tabId: number) => void;
  className?: string;
  children?: ReactNode;
}

export default function TabNavigation({
  tabs,
  activeTab,
  onTabChange,
  className = "",
  children
}: TabNavigationProps) {
  // Filter visible tabs
  const visibleTabs = tabs.filter(tab => tab.visible !== false);
  const totalTabs = visibleTabs.length;

  // Calculate underline position and width
  const getUnderlineStyle = () => {
    const activeIndex = visibleTabs.findIndex(tab => tab.id === activeTab);
    if (activeIndex === -1) return "left-0 w-0";
    
    const widthPercentage = 100 / totalTabs;
    const leftPercentage = (activeIndex * 100) / totalTabs;
    
    return `left-[${leftPercentage}%] w-[${widthPercentage}%]`;
  };

  return (
    <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${className}`}>
      {/* Tab Navigation */}
      <div className="relative flex border-b border-gray-300 mb-7 w-full sm:w-96 max-w-md">
        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            className={`flex-1 py-2 text-center text-sm font-medium transition-colors duration-200 ${
              activeTab === tab.id 
                ? "text-primary-default" 
                : "text-gray-400 hover:text-gray-600"
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
        
        {/* Dynamic underline */}
        <div
          className={`absolute bottom-0 h-1 bg-primary-default transition-all duration-300 ${getUnderlineStyle()}`}
          style={{
            left: `${(visibleTabs.findIndex(tab => tab.id === activeTab) * 100) / totalTabs}%`,
            width: `${100 / totalTabs}%`
          }}
        ></div>
      </div>

      {/* Additional content (like Print button) */}
      {children}
    </div>
  );
}

// Hook for managing tabs with role-based visibility
export function useTabNavigation(initialTab: number = 0, userRole?: string) {
  const [activeTab, setActiveTab] = useState(initialTab);

  // Define tabs with role-based visibility
  const tabs: Tab[] = [
    { id: 0, label: "Pengajuan", visible: true },
    { id: 1, label: "Jadwal Distribusi", visible: true },
    { id: 2, label: "Dokumentasi", visible: userRole === "admin" }
  ];

  // Reset tab if user doesn't have access
  useEffect(() => {
    const visibleTabs = tabs.filter(tab => tab.visible);
    const isActiveTabVisible = visibleTabs.some(tab => tab.id === activeTab);

    if (!isActiveTabVisible) {
      setActiveTab(0);
    }
  }, [userRole, activeTab, tabs]);

  return {
    activeTab,
    setActiveTab,
    tabs
  };
}
