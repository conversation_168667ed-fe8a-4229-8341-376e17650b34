"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Wifi,
  Server,
  Settings
} from "lucide-react";
import { performSystemCheck, EnvironmentCheck } from "@/lib/utils/environment";

interface SystemCheckResult {
  environment: EnvironmentCheck;
  network: boolean;
  api: boolean;
  overall: boolean;
}

interface SystemStatusProps {
  onRetry?: () => void;
  showDetails?: boolean;
}

export default function SystemStatus({ onRetry, showDetails = false }: SystemStatusProps) {
  const [status, setStatus] = useState<SystemCheckResult | null>(null);
  const [isChecking, setIsChecking] = useState(true);

  const checkSystem = async () => {
    setIsChecking(true);
    try {
      const result = await performSystemCheck();
      setStatus(result);
    } catch (error) {
      console.error('System check failed:', error);
      setStatus({
        environment: {
          isValid: false,
          errors: ['System check failed'],
          warnings: []
        },
        network: false,
        api: false,
        overall: false
      });
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkSystem();
  }, []);

  const handleRetry = () => {
    checkSystem();
    if (onRetry) {
      onRetry();
    }
  };

  if (isChecking) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary-default mx-auto mb-4" />
          <p className="text-gray-600">Memeriksa status sistem...</p>
        </div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <XCircle className="w-8 h-8 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600 mb-4">Gagal memeriksa status sistem</p>
          <Button onClick={handleRetry} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Coba Lagi
          </Button>
        </div>
      </div>
    );
  }

  const StatusIcon = ({ isOk }: { isOk: boolean }) => (
    isOk ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    )
  );

  if (status.overall) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="flex items-center gap-2 text-green-600">
          <CheckCircle className="w-5 h-5" />
          <span className="text-sm font-medium">Sistem Normal</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <div className="text-center mb-6">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Masalah Koneksi Terdeteksi
        </h3>
        <p className="text-gray-600 text-sm">
          Beberapa komponen sistem mengalami masalah
        </p>
      </div>

      {/* Status Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Settings className="w-5 h-5 text-gray-500" />
            <span className="text-sm font-medium">Konfigurasi</span>
          </div>
          <StatusIcon isOk={status.environment.isValid} />
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Wifi className="w-5 h-5 text-gray-500" />
            <span className="text-sm font-medium">Koneksi Internet</span>
          </div>
          <StatusIcon isOk={status.network} />
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Server className="w-5 h-5 text-gray-500" />
            <span className="text-sm font-medium">Server API</span>
          </div>
          <StatusIcon isOk={status.api} />
        </div>
      </div>

      {/* Error Details */}
      {showDetails && status.environment.errors.length > 0 && (
        <div className="mb-6 p-4 bg-red-50 rounded-lg border border-red-200">
          <h4 className="font-semibold text-red-800 mb-2">Detail Error:</h4>
          <ul className="text-sm text-red-700 space-y-1">
            {status.environment.errors.map((errorMessage: string, index: number) => (
              <li key={index}>• {errorMessage}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button 
          onClick={handleRetry}
          className="w-full bg-primary-default hover:bg-primary-600 text-white"
          disabled={isChecking}
        >
          {isChecking ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Memeriksa...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4 mr-2" />
              Periksa Ulang
            </>
          )}
        </Button>

        {!status.network && (
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Periksa koneksi internet Anda
            </p>
          </div>
        )}

        {!status.api && status.network && (
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Server sedang tidak dapat diakses
            </p>
          </div>
        )}
      </div>

      {/* Help Text */}
      <div className="mt-6 pt-4 border-t border-gray-200 text-center">
        <p className="text-xs text-gray-500">
          Jika masalah berlanjut, hubungi administrator sistem
        </p>
      </div>
    </div>
  );
}

// Compact version for header/status bar
export function SystemStatusIndicator() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const result = await performSystemCheck();
        setIsOnline(result.overall);
      } catch {
        setIsOnline(false);
      }
    };

    checkStatus();
    const interval = setInterval(checkStatus, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-xs text-gray-600">
        {isOnline ? 'Online' : 'Offline'}
      </span>
    </div>
  );
}
