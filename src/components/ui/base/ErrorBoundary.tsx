"use client";

import { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { Alert<PERSON>riangle, RefreshCw, Home } from "lucide-react";
import { useRouter } from "next/navigation";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Error Fallback Component
function ErrorFallback({ error }: { error?: Error }) {
  const router = useRouter();

  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    router.push('/home');
  };

  const isNetworkError = error?.message.includes('Failed to fetch') || 
                        error?.message.includes('Network') ||
                        error?.name === 'NetworkError';

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {isNetworkError ? 'Koneksi Bermasalah' : 'Terjadi Kesalahan'}
          </h1>
          <p className="text-gray-600">
            {isNetworkError 
              ? 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda dan coba lagi.'
              : 'Maaf, terjadi kesalahan yang tidak terduga. Tim kami telah diberitahu tentang masalah ini.'
            }
          </p>
        </div>

        {/* Error Details (only in development) */}
        {process.env.NODE_ENV === 'development' && error && (
          <div className="mb-6 p-4 bg-gray-100 rounded-lg text-left">
            <h3 className="font-semibold text-sm text-gray-700 mb-2">Error Details:</h3>
            <p className="text-xs text-gray-600 font-mono break-all">
              {error.message}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button 
            onClick={handleRetry}
            className="w-full bg-primary-default hover:bg-primary-600 text-white flex items-center justify-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Coba Lagi
          </Button>
          
          <Button 
            onClick={handleGoHome}
            variant="outline"
            className="w-full flex items-center justify-center gap-2"
          >
            <Home className="w-4 h-4" />
            Kembali ke Beranda
          </Button>
        </div>

        {/* Additional Help */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Jika masalah berlanjut, hubungi tim support atau coba lagi nanti.
          </p>
        </div>
      </div>
    </div>
  );
}

// Network Error Component
export function NetworkErrorPage() {
  const router = useRouter();

  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    router.push('/home');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Tidak Dapat Terhubung
          </h1>
          <p className="text-gray-600">
            Server sedang tidak dapat diakses. Periksa koneksi internet Anda atau coba lagi nanti.
          </p>
        </div>

        <div className="space-y-3">
          <Button 
            onClick={handleRetry}
            className="w-full bg-primary-default hover:bg-primary-600 text-white flex items-center justify-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Coba Lagi
          </Button>
          
          <Button 
            onClick={handleGoHome}
            variant="outline"
            className="w-full flex items-center justify-center gap-2"
          >
            <Home className="w-4 h-4" />
            Kembali ke Beranda
          </Button>
        </div>
      </div>
    </div>
  );
}

// Loading Error Component
export function LoadingErrorComponent({ 
  error, 
  retry 
}: { 
  error: string; 
  retry: () => void; 
}) {
  return (
    <div className="flex flex-col items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200">
      <AlertTriangle className="w-12 h-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-red-800 mb-2">
        Gagal Memuat Data
      </h3>
      <p className="text-red-600 text-center mb-4">
        {error}
      </p>
      <Button 
        onClick={retry}
        variant="outline"
        className="border-red-300 text-red-700 hover:bg-red-50"
      >
        <RefreshCw className="w-4 h-4 mr-2" />
        Coba Lagi
      </Button>
    </div>
  );
}

export default ErrorBoundary;
