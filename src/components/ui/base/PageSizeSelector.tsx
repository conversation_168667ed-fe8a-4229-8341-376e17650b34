"use client";

import { ChevronDown } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface PageSizeSelectorProps {
  currentPageSize: number;
  onPageSizeChange: (pageSize: number) => void;
  options?: number[];
  totalItems?: number;
  className?: string;
}

export default function PageSizeSelector({
  currentPageSize,
  onPageSizeChange,
  options = [10, 20, 50, 100],
  totalItems,
  className = ""
}: PageSizeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handlePageSizeChange = (pageSize: number) => {
    onPageSizeChange(pageSize);
    setIsOpen(false);
  };

  // Calculate range of items being shown
  const getItemRange = () => {
    if (!totalItems) return null;
    
    const start = 1;
    const end = Math.min(currentPageSize, totalItems);
    
    return { start, end };
  };

  const itemRange = getItemRange();

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Show entries text */}
      <span className="text-sm text-gray-600 whitespace-nowrap">
        Tampilkan
      </span>

      {/* Page size dropdown */}
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
        >
          <span className="font-medium">{currentPageSize}</span>
          <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {/* Dropdown menu */}
        {isOpen && (
          <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 min-w-[80px]">
            {options.map((option) => (
              <button
                key={option}
                onClick={() => handlePageSizeChange(option)}
                className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${
                  option === currentPageSize 
                    ? 'bg-primary-50 text-primary-700 font-medium' 
                    : 'text-gray-700'
                }`}
              >
                {option}
              </button>
            ))}
          </div>
        )}
      </div>

      <span className="text-sm text-gray-600 whitespace-nowrap">
        data per halaman
      </span>

      {/* Show current range if totalItems is provided */}
      {itemRange && totalItems && (
        <span className="text-sm text-gray-500 ml-2">
          ({itemRange.start}-{itemRange.end} dari {totalItems} data)
        </span>
      )}
    </div>
  );
}

// Compact version for smaller spaces
export function CompactPageSizeSelector({
  currentPageSize,
  onPageSizeChange,
  options = [10, 20, 50],
  className = ""
}: Omit<PageSizeSelectorProps, 'totalItems'>) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handlePageSizeChange = (pageSize: number) => {
    onPageSizeChange(pageSize);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-2 py-1 text-xs border border-gray-300 rounded bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary-500"
        title={`${currentPageSize} data per halaman`}
      >
        <span>{currentPageSize}</span>
        <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-[60px]">
          {options.map((option) => (
            <button
              key={option}
              onClick={() => handlePageSizeChange(option)}
              className={`w-full px-2 py-1 text-xs text-center hover:bg-gray-50 transition-colors ${
                option === currentPageSize 
                  ? 'bg-primary-50 text-primary-700 font-medium' 
                  : 'text-gray-700'
              }`}
            >
              {option}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Hook for managing page size state
export function usePageSize(initialPageSize: number = 10) {
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [currentPage, setCurrentPage] = useState(1);

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const resetPagination = () => {
    setCurrentPage(1);
  };

  return {
    pageSize,
    currentPage,
    setCurrentPage,
    handlePageSizeChange,
    resetPagination
  };
}
