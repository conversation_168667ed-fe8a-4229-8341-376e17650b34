import FileInput from "@/components/ui/base/file-input";
import FormInput from "@/components/ui/base/form-input";
import FormLabel from "@/components/ui/base/form-label";
import FormSelect from "@/components/ui/base/form-select";
import FormTextArea from "@/components/ui/base/form-text-area";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { postDistributionDocumentData } from "@/lib/distribution/distributionFetching";
import { postJenisTanamanData } from "@/lib/master/jenisTanamanFetching";
import { error } from "console";
import { XIcon } from "lucide-react";
import React, { useState } from "react";
import { Bounce, toast } from "react-toastify";

/**
 * ModalProps is an interface for the properties of the modal component.
 * @interface
 */
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onChange: (value: string) => void;
  status?: string;
  value?: string;
  errorMessage?: string | null;
  distributionIdValue: number;
}

/**
 * DocumentSubmission component renders a modal dialog with terms and conditions.
 *
 * @component
 * @param {ModalProps} props - The properties for the modal component.
 * @param {boolean} props.isOpen - Determines if the modal is open or closed.
 * @param {() => void} props.onClose - Function to call when the modal is closed.
 *
 * @returns {JSX.Element | null} The rendered modal component or null if not open.
 */
const DocumentSubmission: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  status,
  value,
  errorMessage,
  onSubmit,
  onChange,
  distributionIdValue,
}) => {
  // Move all hooks to the top, before any conditional returns
  const { getToken } = useAuth();
  const token = getToken();
  const [isLoading, setIsLoading] = useState(false);
  const [messageError, setMessageError] = useState<{ distribusiId?: string; penerimaTanaman?: string }>({
    distribusiId: "",
    penerimaTanaman: "",
  });
  const [formData, setFormData] = useState({
    namaPenerima: "",
    foto: null as File | null,
    file: null as File | null,
    catatan: "",
  });

  const clearMessageError = () => {
    setMessageError({
      distribusiId: "",
      penerimaTanaman: "",
    });
  };

  // Early return after all hooks
  if (!isOpen) return null;

  const handleSubmit = async () => {
    setIsLoading(true);

    const payload = {
      distribusiId: distributionIdValue,
      penerimaTanaman: formData.namaPenerima,
      foto: formData.foto,
      file: formData.file,
      catatan: formData.catatan,
    }
    
    await postDistributionDocumentData(payload, String(token))
      .then((response) => {
        if (!response.ok) {
          response.json().then((errorData) => {
            setMessageError(errorData.data);
            if (formData.foto === null) {
              toast.error(errorData.message, {
              position: "top-right",
              autoClose: 5000,
              hideProgressBar: false,
              closeOnClick: false,
              pauseOnHover: true,
              draggable: true,
              progress: undefined,
              theme: "light",
              transition: Bounce,
            });
            }
          });

          throw new Error("Failed to save data");
        }
        return response.json();
      })
      .then((data) => {
        toast.success("Data berhasil disimpan", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });

        clearMessageError();
        setIsLoading(false);
        // router.push('/home/<USER>/poktan');
      })
      .catch((error) => {
        setIsLoading(false);
        console.error("Error:", error);
        toast.error(`${error}`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      });
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 w-[50rem]">
        <h2 className="text-2xl font-semibold mb-4 text-center flex justify-between items-center">
          {status}
          <XIcon className="h-6 w-6 cursor-pointer" onClick={onClose} />
        </h2>
        <FormInput
          label="Penerima Tanaman"
          value={formData.namaPenerima}
          onChange={(e: string) =>
            setFormData({ ...formData, namaPenerima: e })
          }
          required
          placeholder="Masukan Nama Penerima"
          errorMessage={messageError?.penerimaTanaman || ""}
        />
        <div className="text-sm font-medium text-gray-700 mb-2 mt-4">
          Foto <span className="text-danger-600"></span>
        </div>
        <div className="flex gap-8 mb-4">
          <label
            htmlFor="foto-upload"
            className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg w-[20rem] h-[16rem] cursor-pointer transition hover:border-primary-400"
          >
            <span className="text-2xl text-gray-600 font-medium">
              + Tambah Foto
            </span>
            <input
              id="foto-upload"
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                if (e.target.files && e.target.files[0]) {
                  setFormData({ ...formData, foto: e.target.files[0] });
                }
              }}
              required
            />
          </label>
          {formData.foto ? (
            <img
              src={URL.createObjectURL(formData.foto)}
              alt="Preview"
              className="object-cover rounded-lg w-[20rem] h-[16rem]"
            />
          ) : (
            <div className="bg-gray-200 rounded-lg w-[20rem] h-[16rem] flex items-center justify-center text-gray-400">
              Preview Foto
            </div>
          )}
        </div>

        <div className="text-sm font-medium text-gray-700 mb-2">File</div>
        <div className="relative flex items-center border border-gray-300 w-full rounded-full p-2 px-3 mb-4">
          <input
            id="file-upload"
            type="file"
            accept=".pdf,.doc,.docx"
            className="hidden"
            onChange={(e) => {
              if (e.target.files && e.target.files[0]) {
                setFormData({ ...formData, file: e.target.files[0] });
              }
            }}
          />
          <label htmlFor="file-upload" className="cursor-pointer flex-1">
            {formData.file ? formData.file.name : "Pilih file dokumentasi..."}
          </label>
          {formData.file && (
            <button
              type="button"
              className="absolute right-2 bg-primary-100 text-primary-default px-4 py-1 rounded-full"
              onClick={() => {
                const url = URL.createObjectURL(formData.file as File);
                window.open(url, "_blank");
              }}
            >
              Lihat
            </button>
          )}
        </div>

        <FormTextArea
          label={"Catatan"}
          value={formData.catatan}
          onChange={(e: string) => setFormData({ ...formData, catatan: e })}
          required
          placeholder="Masukan Catatan"
        />

        <div className="mt-4 flex justify-end gap-4">
          <Button
            onClick={onClose}
            className="border border-primary-default rounded-full text-primary-default px-5"
          >
            {status === "Tambah" || status === "Edit" ? "Batal" : "Tutup"}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-success-700 rounded-full text-white px-5"
          >
            {isLoading ? "Loading..." : "Selesaikan"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DocumentSubmission;
