"use client";

import FileInput from "@/components/ui/base/file-input";
import FormLabel from "@/components/ui/base/form-label";
import PageLoading from "@/components/ui/loading/PageLoading";
import { useAuth } from "@/hooks/useAuth";
import { fetchDistributionDataById } from "@/lib/distribution/distributionFetching";
import { getMetodePenanamanById } from "@/lib/master/metodePenanamanFetching";
import { getstatusKepemilikanById } from "@/lib/master/statusKepemilikanFetching";
import { useCallback, useEffect, useState } from "react";

// Define the interface for form data
interface FormData {
  namaLengkap?: string;
  nik?: string;
  noTelepon?: string;
  email?: string;
  noKartuTani?: string | number;
  poktanId?: string;
  noRegistrasiPoktan?: string;
  namaKetuaPoktan?: string;
  Provinsi?: string;
  Kabupaten?: string;
  Kecamatan?: string;
  DesaKelurahan?: string;
  alamat?: string;
  luasLahan?: number;
  tanamanId?: string;
  jumlahTanaman?: number;
  masaTanam?: string;
  tahunMusimTanam?: number;
  statusLahanId?: string;
  methodId?: string;
  alasan?: string;
  ktp?: File | string;
  kartuTani?: File | string;
}

// Define props interface
interface TabSubmissionProps {
  formData: FormData;
  setFormDataChild: (data: FormData) => void;
  isLoading?: boolean;
}

// Skeleton component for loading state
const SkeletonField = () => (
  <div className="space-y-2">
    <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
    <div className="h-10 bg-gray-200 rounded animate-pulse w-full"></div>
  </div>
);

const SkeletonSection = ({ title, fieldCount = 6 }: { title: string; fieldCount?: number }) => (
  <div>
    <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4 mb-4"></div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Array.from({ length: fieldCount }).map((_, index) => (
        <SkeletonField key={index} />
      ))}
    </div>
  </div>
);

export default function TabSubmission({ formData, setFormDataChild, isLoading = false }: TabSubmissionProps) {

  // Show skeleton loading state
  if (isLoading) {
    return (
      <PageLoading/>
    );
  }

  return (
    <div>
      <div className="text-lg font-medium mb-4">Data diri</div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <FormLabel
        label="Nama Lengkap"
        value={formData.namaLengkap ?? "-"}
        required
      />
      <FormLabel label="NIK" value={formData.nik ?? "-"} required />
      <FormLabel
        label="Nomor Telepon"
        value={formData.noTelepon ?? "-"}
        required
      />
      <FormLabel label="Email" value={formData.email ?? "-"} />
      <FormLabel
        label="Nomor Kartu Tani (Jika Ada)"
        value={String(formData.noKartuTani) ?? "-"}
      />
      <FormLabel
        label="Nama Kelompok Tani (Poktan)"
        value={formData.poktanId ?? "-"}
        required
      />
      <FormLabel
        label="Nomor Registrasi Poktan"
        value={formData.noRegistrasiPoktan ?? "-"}
      />
      <FormLabel
        label="Nama Ketua Poktan"
        value={formData.namaKetuaPoktan ?? "-"}
      />
      <FormLabel label="Provinsi" value={formData.Provinsi ?? "-"} required />
      <FormLabel
        label="Kabupaten"
        value={formData.Kabupaten ?? "-"}
        required
      />
      <FormLabel
        label="Kecamatan"
        value={formData.Kecamatan ?? "-"}
        required
      />
      <FormLabel
        label="Desa/Kelurahan"
        value={formData.DesaKelurahan ?? "-"}
        required
      />
      <FormLabel label="Alamat" value={formData.alamat ?? "-"} required />
    </div>
      <div className="text-lg font-medium mb-4 mt-7">
        Data Lahan dan Usaha Tani
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormLabel
          label="Luas Lahan (Ha)"
          value={String(formData.luasLahan)}
          required
        />
        <FormLabel label="Jenis Tanaman" value={formData.tanamanId ?? "-"} required />
        <FormLabel
          label="Jumlah Tanaman dalam Satuan Hektarc(Ha)"
          value={String(formData.jumlahTanaman ?? 0)}
          required
        />
        <FormLabel label="Masa Tanam" value={formData.masaTanam ?? "-"} required />
        <FormLabel
          label="Tahun Musim Tanam"
          value={String(formData.tahunMusimTanam)}
          required
        />
        <FormLabel
          label="Status kepemilikan Lahan"
          value={formData.statusLahanId ?? "-"}
          required
        />
      </div>
      <div className="text-lg font-medium mb-4 mt-7">Data Kebutuhan Pupuk</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormLabel
          label="Jenis Pupuk yang Diajukan"
          value={formData.poktanId ?? "-"}
          required
        />
        <FormLabel
          label="Jumlah Pupuk yang Diajukan"
          value={String(formData.jumlahTanaman ?? 0)}
          required
        />
        <FormLabel
          label="Metode Penanaman Tanaman"
          value={formData.methodId ?? "-"}
          required
        />
        <FormLabel label="Alasan Pengajuan Pupuk" value={formData.alasan ?? "-"} />
      </div>
      <div className="text-lg font-medium mb-4 mt-7">Upload</div>
      <div className="flex flex-col gap-2">
        <div className="flex flex-col items-start w-full gap-1">
          <label htmlFor="ktp-file-input" className="block text-sm font-medium">
            KTP (JPG/PNG/PDF)
          </label>
          <FileInput
            file={formData.ktp ?? ""}
            onChange={(file: File | string) => {
              setFormDataChild({ ...formData, ktp: file });
            }}
          />
        </div>
        <div className="flex flex-col items-start w-full gap-1">
          <label htmlFor="ktp-file-input" className="block text-sm font-medium">
            Kartu Tani (JPG/PNG/PDF)
          </label>
          <FileInput
            file={formData.kartuTani ?? ""}
            onChange={(file: File | string) => {
              if (file instanceof File) {
                setFormDataChild({ ...formData, kartuTani: file.name });
              }
            }}
          />
        </div>
      </div>
    </div>
  );
}
