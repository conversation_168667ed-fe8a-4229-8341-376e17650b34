"use client";

import FormInput from "@/components/ui/base/form-input";
import FormLabel from "@/components/ui/base/form-label";
import FormSelect from "@/components/ui/base/form-select";
import { Button } from "@/components/ui/button";
import DatePicker from "@/components/ui/date-picker";
import { useAuth } from "@/hooks/useAuth";
import { fetchJenisTanamanData } from "@/lib/master/jenisTanamanFetching";
import { usePermission } from "@/store/usePermission";
import { distribusiTanaman } from "@/types/distribution/distribution";
import { JenisTanaman } from "@/types/master/jenisTanaman";
import { is } from "date-fns/locale";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import DocumentSubmissionModal from "./DocumentSubmissionModal";
import { DistribusiMetode } from "@/types/master/distribusiMetode";
import { fetchDistribusiData } from "@/lib/master/distribusiMetodeFetching";
import { postDistributionScheduleData } from "@/lib/distribution/distributionFetching";
import { Bounce, toast } from "react-toastify";

interface TabDistributionScheduleProps {
  formData: {
    tanggalDistribusi: string;
    metodeDistribusi: string;
    namaPetugas: string;
    noTeleponPetugas: string;
    jenisTanaman: string;
    jumlahTanaman: string;
  };
  slug: string;
  distribusiTanaman: distribusiTanaman[];
  metodeDistribusi?: string[];
}
export default function TabDistributionSchedule({
  formData,
  slug,
  distribusiTanaman,
}: TabDistributionScheduleProps) {
  const { getToken } = useAuth();
  const token = getToken();
  const role = usePermission((state: { role: any }) => state.role);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const [isLoadingButton, setIsLoadinButton] = useState(false);
  const [listPlant, setListPlant] = useState<JenisTanaman[]>([]);
  const [listDistribution, setListDistribution] = useState<DistribusiMetode[]>(
    []
  );
  const [messageError, setMessageError] = useState({
    tanggalDistribusi: "",
    metodeDistribusi: "",
    namaPetugasDistribusi: "",
    kontakPetugasDistribusi: "",
    jenisTanaman: "",
    jumlahBibitTanaman: "",
  });
  const clearMessageError = () => {
    setMessageError({
      tanggalDistribusi: "",
      metodeDistribusi: "",
      namaPetugasDistribusi: "",
      kontakPetugasDistribusi: "",
      jenisTanaman: "",
      jumlahBibitTanaman: "",
    });
  };
  const [formDataSchedule, setFormDataSchedule] = useState({
    tanggalDistribusi: new Date(),
    metodeDistribusi: 0,
    namaPetugas: formData.namaPetugas || "",
    noTeleponPetugas: "",
    jenisTanaman: 0,
    jumlahTanaman: 0,
  });
  const fetchListPlant = async () => {
    const data = await fetchJenisTanamanData(1, String(token));
    setListPlant(data.items);

    const distributionData = await fetchDistribusiData(1, String(token));
    setListDistribution(distributionData.items);
  };
  const handleJadwalkan = async () => {
    const id = Number(new URLSearchParams(window.location.search).get("id"));
    setIsLoadinButton(true);
    const payload = {
      tanggalDistribusi: formDataSchedule.tanggalDistribusi,
      namaPetugasDistribusi: formDataSchedule.namaPetugas,
      kontakPetugasDistribusi: formDataSchedule.noTeleponPetugas,
      jumlahBibitTanaman: formDataSchedule.jumlahTanaman,
      pengajuanId: id,
      distributionMethodId: formDataSchedule.metodeDistribusi,
      tanamanId: formDataSchedule.jenisTanaman,
    };

    await postDistributionScheduleData(payload, String(token))
      .then((response) => {
        if (!response.ok) {
          response.json().then((errorData) => {
            setMessageError(errorData.data);
          });

          throw new Error("Failed to save data");
        }
        return response.json();
      })
      .then((data) => {
        toast.success("Data berhasil disimpan", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });

        clearMessageError();
        setIsLoadinButton(false);
        router.refresh();
        // router.push('/home/<USER>/poktan');
      })
      .catch((error) => {
        setIsLoadinButton(false);
        console.error("Error:", error);
        toast.error(`${error}`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      });
  };

  useEffect(() => {
    fetchListPlant();
  }, []);
  return (
    <div className="mt-4">
      {role === "distributor" && distribusiTanaman.length > 0 && (
        <div className="flex justify-end mb-4">
          <Button
            onClick={() => {
              // router.push(`/home/<USER>/${slug}/documentation`);
              setIsModalOpen(true);
            }}
            className="bg-success-700 text-white px-8 tex-sm rounded-full"
          >
            Selesaikan
          </Button>
        </div>
      )}
      {distribusiTanaman.length > 0 &&
        distribusiTanaman.map((item, idx) => (
          <div key={idx} className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormLabel
              label="Tanggal Distribusi"
              value={
                item.tanggalDistribusi
                  ? new Date(item.tanggalDistribusi).toLocaleDateString(
                      "id-ID",
                      {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                      }
                    )
                  : ""
              }
              required
            />
            <FormLabel
              label="Metode Distribusi"
              value={String(item.distributionMethodId)}
              required
            />
            <FormLabel
              label="Nama Petugas Distribusi"
              value={item.namaPetugasDistribusi}
              required
            />
            <FormLabel
              label="Kontak Petugas Distribusi"
              value={item.kontakPetugasDistribusi}
              required
            />
            <FormLabel
              label="Jenis Tanaman yang Dikirim"
              value={
                listPlant.find((plant) => plant.id === item.tanamanId)?.name ||
                "Tidak Diketahui"
              }
              required
            />
            <FormLabel
              label="Jumlah Bibit Tanaman"
              value={String(item.jumlahBibitTanaman)}
              required
            />
          </div>
        ))}
      {distribusiTanaman.length === 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DatePicker
            label="Tanggal Pengajuan"
            date={formDataSchedule.tanggalDistribusi}
            onSelect={(date: Date) =>
              setFormDataSchedule({
                ...formDataSchedule,
                tanggalDistribusi: date,
              })
            }
            //   errorMessage={messageError.tanggalPengajuan}
          />
          <FormSelect
            label="Metode Distribusi"
            value={listDistribution.map((value) => value.name)}
            selected={
              listDistribution.find(
                (plant) => plant.id === formDataSchedule.metodeDistribusi
              )?.name || ""
            }
            onChange={(value: string) => {
              const selectedPlant = listDistribution.find(
                (plant) => plant.name === value
              );
              if (selectedPlant) {
                setFormDataSchedule({
                  ...formDataSchedule,
                  metodeDistribusi: selectedPlant.id,
                });
              }
            }}
            errorMessage={messageError.metodeDistribusi}
            required
          />
          <FormInput
            label="Nama Petugas Distribusi"
            placeholder="Masukkan nama petugas distribusi"
            value={formDataSchedule.namaPetugas}
            onChange={(value: string) =>
              setFormDataSchedule({
                ...formDataSchedule,
                namaPetugas: value,
              })
            }
            errorMessage={messageError.namaPetugasDistribusi}
            required
          />
          <FormInput
            label="Kontak Petugas Distribusi"
            placeholder="Masukkan kontak petugas distribusi"
            value={formDataSchedule.noTeleponPetugas}
            onChange={(value: string) =>
              setFormDataSchedule({
                ...formDataSchedule,
                noTeleponPetugas: value,
              })
            }
            required
            type="number"
            errorMessage={messageError.kontakPetugasDistribusi}
          />
          <FormSelect
            label="Jenis Tanaman"
            value={listPlant.map((value) => value.name)}
            selected={
              listPlant.find(
                (plant) => plant.id === formDataSchedule.jenisTanaman
              )?.name || ""
            }
            onChange={(value: string) => {
              const selectedPlant = listPlant.find(
                (plant) => plant.name === value
              );
              if (selectedPlant) {
                setFormDataSchedule({
                  ...formDataSchedule,
                  jenisTanaman: selectedPlant.id,
                });
              }
            }}
            required
            errorMessage={messageError.jenisTanaman}
          />
          <FormInput
            label="Jumlah Bibit Tanaman"
            placeholder="Masukkan jumlah bibit tanaman"
            value={formDataSchedule.jumlahTanaman.toString()}
            onChange={(value: string) =>
              setFormDataSchedule({
                ...formDataSchedule,
                jumlahTanaman: parseInt(value, 10),
              })
            }
            required
            type="number"
            errorMessage={messageError.jumlahBibitTanaman}
          />
          <div className="col-span-2 flex justify-end gap-4">
            <Button
              onClick={() => {
                router.back();
              }}
              className="border border-primary-default text-primary-default px-8 tex-sm rounded-full"
            >
              Batal
            </Button>
            <Button
              onClick={() => {
                clearMessageError();
                handleJadwalkan();
              }}
              className="bg-primary-default text-white px-8 tex-sm rounded-full"
            >
              {isLoadingButton ? "Loading..." : "Jadwalkan"}
            </Button>
          </div>
        </div>
      )}
      <DocumentSubmissionModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        status="Selesaikan Distribusi"
        value=""
        errorMessage={null}
        onSubmit={() => {}}
        onChange={() => {}}
        distributionIdValue={distribusiTanaman.length > 0 ? distribusiTanaman[0].id : 0}
      />
    </div>
  );
}
