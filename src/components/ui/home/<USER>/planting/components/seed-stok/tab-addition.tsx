"use client";

import {
  Table,
  TableBody,
  TableCell,
  Table<PERSON>ooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";

interface TabAddition {
  listPenambahan: {
    tanggal_pembahan: string;
    jumlah_bibit: string;
  }[];
}

export default function TabAddition(props: TabAddition) {
  const { listPenambahan } = props;
  return (
    <div>
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200">No</TableHead>
            <TableHead className="bg-gray-200 text-center">
              Tanggal Penambahan
            </TableHead>
            <TableHead className="bg-gray-200 text-center">
              Jumlah Bibit Tanaman yang Ditambahkan
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {listPenambahan.map((value) => (
            <TableRow key={listPenambahan.indexOf(value)}>
              <TableCell className="w-[50px]">
                {listPenambahan.indexOf(value) + 1}
              </TableCell>
              <TableCell className="text-center">
                {new Date(value.tanggal_pembahan).toLocaleDateString("id-ID", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })}
              </TableCell>
              <TableCell className="text-center">
                {value.jumlah_bibit}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        {/* <TableFooter>
                    <TableRow>
                        <TableCell colSpan={8} className="text-right">
                            <div className="w-full h-full flex justify-end items-center gap-5">
                                <div className="relative text-center text-[#597445] text-sm font-poppins font-normal leading-[30px] break-words">
                                    10 dari 230 total data
                                </div>
                                <div className="flex justify-center items-center gap-6">
                                    <div className="p-2 bg-[#FCFBFB] rounded-md border border-[#BDBDC2] flex justify-center items-center gap-2">
                                        <div className="relative text-[#597445] text-sm font-inter font-medium leading-4 break-words">
                                            1
                                        </div>
                                        <div className="w-4 h-4 relative">
                                            <ChevronDown className="w-4 h-4 text-[#597445]" />
                                        </div>
                                    </div>
                                    <div className="w-[235px] flex justify-between items-start">
                                        <div className="w-10 py-2 bg-[#FCFBFB] rounded-md border border-[#BDBDC2] flex flex-col justify-center items-center">
                                            <div className="w-4 h-4 relative">
                                                <ChevronLeft className="w-4 h-4 text-[#597445]" />
                                            </div>
                                        </div>
                                        <div className="px-4 py-2 bg-[#597445] rounded-md flex justify-center items-center gap-2">
                                            <div className="relative text-white text-sm font-inter font-medium leading-4 break-words">
                                                1
                                            </div>
                                        </div>
                                        <div className="w-10 px-4 py-2 bg-[#FCFBFB] rounded-md border border-[#BDBDC2] flex justify-center items-center gap-2">
                                            <div className="relative text-[#597445] text-sm font-inter font-medium leading-4 break-words">
                                                ...
                                            </div>
                                        </div>
                                        <div className="px-4 py-2 bg-[#FCFBFB] rounded-md border border-[#BDBDC2] flex justify-center items-center gap-2">
                                            <div className="relative text-[#597445] text-sm font-inter font-medium leading-4 break-words">
                                                5
                                            </div>
                                        </div>
                                        <div className="w-10 h-9 bg-[#FCFBFB] rounded-md border border-[#BDBDC2] flex flex-col justify-center items-center">
                                            <div className="w-4 h-4 relative">
                                                <ChevronRight className="w-4 h-4 text-[#597445]" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TableCell>
                    </TableRow>
                </TableFooter> */}
      </Table>
    </div>
  );
}
