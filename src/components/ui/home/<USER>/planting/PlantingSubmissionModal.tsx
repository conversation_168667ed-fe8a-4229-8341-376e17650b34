import FormInput from "@/components/ui/base/form-input";
import FormSelect from "@/components/ui/base/form-select";
import { Button } from "@/components/ui/button";
import DatePicker from "@/components/ui/date-picker";
import { useAuth } from "@/hooks/useAuth";
import { updateStatusSubmissionPlantData } from "@/lib/planting/submissionPlantFetching";
import { XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { Bounce, toast } from "react-toastify";

/**
 * ModalProps is an interface for the properties of the modal component.
 * @interface
 */
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * PlantingSubmissionModal component renders a modal dialog with terms and conditions.
 *
 * @component
 * @param {ModalProps} props - The properties for the modal component.
 * @param {boolean} props.isOpen - Determines if the modal is open or closed.
 * @param {() => void} props.onClose - Function to call when the modal is closed.
 *
 * @returns {JSX.Element | null} The rendered modal component or null if not open.
 */
const PlantingSubmissionModal: React.FC<ModalProps> = ({ isOpen, onClose }) => {
  const { getToken } = useAuth();
  const token = getToken();
  const router = useRouter();
  const [formData, setFormData] = useState({
    tanggal: new Date(),
    jenis_tanaman: "",
    stok: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const updateStatus = async () => {
    setIsLoading(true);
    const id = Number(new URLSearchParams(window.location.search).get("id"));
    await updateStatusSubmissionPlantData({
      status: "Disetujui",
    }, String(id), String(token))
      .then((response) => {
        if (!response.ok) {
          // response.json().then((errorData) => {
          //   setMessageError(errorData.data);
          // });

          throw new Error("Failed to save data");
        }
        return response.json();
      })
      .then((data) => {
        toast.success("Data berhasil disimpan", {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });

        setIsLoading(false);
        onClose();
        router.push("/home/<USER>/submission-plant");
      })
      .catch((error) => {
        setIsLoading(false);
        console.error("Error:", error);
        toast.error(`${error}`, {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: false,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
          transition: Bounce,
        });
      });
  };
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 w-[calc(100% - 1rem)] relative">
        <div className="mb-4 flex flex-col gap-2">
          <div className="text-2xl font-semibold">Form Penerimaan</div>
          <div className="text-sm font-medium text-gray-400">
            Jika disetujui, silahkan masukan stok pupuk yang akan di kirim.
          </div>
        </div>

        <div className="flex flex-col">
          <div className="flex items-center justify-between gap-2 mb-4">
            <DatePicker
              label="Tanggal Penerimaan"
              date={formData.tanggal}
              onSelect={(date: Date) =>
                setFormData({ ...formData, tanggal: date })
              }
            />
            <FormSelect
              label="Jenis Tanaman"
              value={["Padi", "Jagung"]}
              selected={formData.jenis_tanaman}
              required
            />
          </div>
          <FormInput
            label="Stok Tanaman"
            placeholder="Masukan Status Stok Tanaman"
            value={""}
            required
          />
        </div>

        <div className="mt-4 flex justify-end gap-4">
          <Button
            onClick={onClose}
            className="border border-primary-default rounded-full text-primary-default px-5"
          >
            Batal
          </Button>
          <Button
            onClick={() => updateStatus()}
            className="bg-primary-default rounded-full text-white px-5"
          >
            {isLoading ? (
              <span>Loading...</span>
            ) : (
              <span>Kirim</span>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PlantingSubmissionModal;
