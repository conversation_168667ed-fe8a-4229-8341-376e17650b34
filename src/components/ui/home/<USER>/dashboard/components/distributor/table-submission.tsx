"use client";

import {
  <PERSON>,
  TableBody,
  <PERSON><PERSON>ell,
  <PERSON><PERSON><PERSON>er,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  EllipsisVertical,
  Printer,
} from "lucide-react";
import { useEffect, useState } from "react";
import { fetchDistributorDashboard } from "@/lib/dashboard/dashboardFetching";
import { StokBibit } from "@/types/dashboard/distributorDashboard";
import { useAuth } from "@/hooks/useAuth";
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";

export default function TableSubmission() {
  const [listTabFilter, setSelectTabFilter] = useState<
    { name: string; select: boolean }[]
  >([
    {
      name: "<PERSON><PERSON><PERSON>",
      select: true,
    },
    {
      name: "<PERSON>ung<PERSON>",
      select: false,
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      select: false,
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      select: false,
    }
  ]);
  const { getToken } = use<PERSON>uth();
  const token = getToken();

  const [listStokBibit, setListStokBibit] = useState<StokBibit[]>([]);

  // Page size management
  const { pageSize, currentPage, setCurrentPage, handlePageSizeChange } = usePageSize(10);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);

  const fetchPage = async () => {
    setLoading(true);
    const res = await fetchDistributorDashboard(String(token));
    if (res) {
      setListStokBibit(res.stokBibit);
      setTotalItems(res.stokBibit.length);
    }
    setLoading(false);
  }

  useEffect(() => {
    fetchPage();
  }, []);

  // Update total items when listStokBibit changes
  useEffect(() => {
    setTotalItems(listStokBibit.length);
  }, [listStokBibit]);
  return (
    <div className="bg-white p-4 rounded-md shadow-md font-poppins mt-4">
      <div className="flex justify-between items-center gap-4 mb-4">
        <div className="bg-white rounded-full p-2 shadow-lg">
          {listTabFilter.map((tab, index) => (
            <button
              key={index}
              onClick={() => {
                const updatedTabs = listTabFilter.map((t, i) => ({
                  ...t,
                  select: i === index,
                }));
                setSelectTabFilter(updatedTabs);
              }}
              className={`rounded-full p-2 px-5 transition-all duration-300 ease-in-out ${
                tab.select ? "bg-primary-default text-white" : ""
              }`}
            >
              <span className="text-sm">{tab.name}</span>
            </button>
          ))}
        </div>
        <button className="border border-neutral-70 text-primary-default rounded-full p-2 px-5 flex items-center gap-2">
          <Printer className="h-6 w-6" />
          Print
        </button>
      </div>
      <Table className="mt-4 overflow-hidden">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] bg-gray-200">No</TableHead>
            {/* <TableHead className="w-[100px] bg-gray-200">Tanggal</TableHead> */}
            <TableHead className="bg-gray-200">Nama</TableHead>
            {/* <TableHead className="bg-gray-200">Poktan</TableHead> */}
            <TableHead className="text-right bg-gray-200">
              Jenis Tanaman
            </TableHead>
            {/* <TableHead className="text-right bg-gray-200">Luas Tanah</TableHead>
            <TableHead className="text-right bg-gray-200">
              Jumlah Bibit Yang Diajukan
            </TableHead>
            <TableHead className="text-right bg-gray-200">Status</TableHead>
            <TableHead className="text-right bg-gray-200"></TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {listStokBibit.map((value) => (
            <TableRow key={listStokBibit.indexOf(value)}>
              <TableCell className="w-[50px]">
                {listStokBibit.indexOf(value) + 1}
              </TableCell>
              {/* <TableCell className="font-medium">{value.tanggal}</TableCell> */}
              <TableCell>{value.namaTanaman}</TableCell>
              {/* <TableCell>{value.poktan}</TableCell>
              <TableCell className="text-right">
                {value.jenis_tanaman}
              </TableCell>
              <TableCell className="text-right">{value.luas_tanah}</TableCell> */}
              <TableCell className="text-right">{value.jumlah}</TableCell>
              {/* <TableCell className="text-right">{value.status}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger className="border-none bg-transparent active:border-none focus:border-none">
                      <EllipsisVertical className="cursor-pointer" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-white shadow-md rounded-md absolute left-[-110px]">
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={() => {}}
                      >
                        Detail
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell> */}
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={9}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={Math.ceil(totalItems / pageSize)}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={setCurrentPage}
                onPageSizeChange={handlePageSizeChange}
                isLoading={loading}
                className="mt-4"
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
}
