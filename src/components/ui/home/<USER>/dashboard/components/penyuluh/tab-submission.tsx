"use client";
import { <PERSON>geX, CircleCheckBig, CircleX, Clock, Send } from "lucide-react";
import { LineChartInfo } from "../../line-chart-info";
import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState } from "react";
import { BarChartInfo } from "../../bar-chart-info";
import { TinyBarChart } from "../../tiny-chart-info";

interface CardsPenyuluh {
  pengajuan: {
    sedangDiproses: number;
    disetujui: number;
    diperbaiki: number;
    direvisi: number;
  };
}

interface ChartPengajuanBulanan {
  bulan: string;
  jumlah: number;
}

export default function TabSubmission() {
  const { getToken } = useAuth();
  const token = getToken();
  const [card, setCard] = useState<CardsPenyuluh>(() => ({
    pengajuan: {
      sedangDiproses: 0,
      disetujui: 0,
      diperbaiki: 0,
      direvisi: 0,
    },
  }));
  const [chartPengajuanBulanan, setChartPengajuanBulanan] = useState<ChartPengajuanBulanan[]>([]);

  const fetchPage = async () => {
    // TODO: Implement penyuluh dashboard API call
    // For now, using mock data
    setCard({
      pengajuan: {
        sedangDiproses: 5,
        disetujui: 12,
        diperbaiki: 2,
        direvisi: 3,
      },
    });
    setChartPengajuanBulanan([
      { bulan: "Jan", jumlah: 10 },
      { bulan: "Feb", jumlah: 15 },
      { bulan: "Mar", jumlah: 8 },
      { bulan: "Apr", jumlah: 20 },
      { bulan: "May", jumlah: 12 },
      { bulan: "Jun", jumlah: 18 },
    ]);
  };

  useEffect(() => {
    fetchPage();
  }, []);

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="text-lg font-medium mb-4">Status Pengajuan</div>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <Clock className="h-8 w-8 text-warning-600" />
              <div className="text-primary-default">Sedang Diproses</div>
              <div className="text-black font-medium text-2xl">{card.pengajuan.sedangDiproses ?? 0}</div>
            </div>
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <CircleCheckBig className="h-8 w-8 text-success-600" />
              <div className="text-primary-default">Disetujui</div>
              <div className="text-black font-medium text-2xl">{card.pengajuan.disetujui ?? 0}</div>
            </div>
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <Send className="h-8 w-8 text-info-600" />
              <div className="text-primary-default">Diperbaiki</div>
              <div className="text-black font-medium text-2xl">{card.pengajuan.diperbaiki ?? 0}</div>
            </div>
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <CircleX className="h-8 w-8 text-error-600" />
              <div className="text-primary-default">Direvisi</div>
              <div className="text-black font-medium text-2xl">{card.pengajuan.direvisi ?? 0}</div>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <div className="text-lg font-medium mb-4">
            Pengajuan Bulanan
          </div>
          <TinyBarChart
            data={
              chartPengajuanBulanan.map((item) => ({
                month: item.bulan,
                desktop: item.jumlah,
              }))
            }
            barColor="#80AC6C"
            width={400}
            height={40}
          />
        </div>
      </div>
    </div>
  );
}
