"use client";
import { <PERSON><PERSON><PERSON>, CircleCheck<PERSON>ig, <PERSON><PERSON>, <PERSON>, Send } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState } from "react";
import { TinyBarChart } from "../../tiny-chart-info";

interface CardsDistribusi {
  dalamProses: number;
  dijadwalkan: number;
  selesai: number;
}

interface ChartDistribusiBulanan {
  bulan: string;
  jumlah: number;
}

export default function TabDistribution() {
  const { getToken } = useAuth();
  const token = getToken();
  const [card, setCard] = useState<CardsDistribusi>(() => ({
    dalamProses: 0,
    dijadwalkan: 0,
    selesai: 0,
  }));
  const [chartDistribusiBulanan, setChartDistribusiBulanan] = useState<ChartDistribusiBulanan[]>([]);

  const fetchPage = async () => {
    // TODO: Implement penyuluh distribution dashboard API call
    // For now, using mock data
    setCard({
      dalamProses: 3,
      dijadwalkan: 7,
      selesai: 15,
    });
    setChartDistribusiBulanan([
      { bulan: "Jan", jumlah: 8 },
      { bulan: "Feb", jumlah: 12 },
      { bulan: "Mar", jumlah: 6 },
      { bulan: "Apr", jumlah: 15 },
      { bulan: "May", jumlah: 10 },
      { bulan: "Jun", jumlah: 14 },
    ]);
  };

  useEffect(() => {
    fetchPage();
  }, []);

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="text-lg font-medium mb-4">Status Distribusi</div>
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <Clock className="h-8 w-8 text-warning-600" />
              <div className="text-primary-default">Dalam Proses</div>
              <div className="text-black font-medium text-2xl">{card.dalamProses ?? 0}</div>
            </div>
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <Send className="h-8 w-8 text-info-600" />
              <div className="text-primary-default">Dijadwalkan</div>
              <div className="text-black font-medium text-2xl">{card.dijadwalkan ?? 0}</div>
            </div>
            <div className="flex flex-col items-center gap-4 h-auto bg-primary-100 rounded-md p-4">
              <CircleCheckBig className="h-8 w-8 text-success-600" />
              <div className="text-primary-default">Selesai</div>
              <div className="text-black font-medium text-2xl">{card.selesai ?? 0}</div>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-4">
          <div className="text-lg font-medium mb-4">
            Distribusi Bulanan
          </div>
          <TinyBarChart
            data={
              chartDistribusiBulanan.map((item) => ({
                month: item.bulan,
                desktop: item.jumlah,
              }))
            }
            barColor="#80AC6C"
            width={400}
            height={40}
          />
        </div>
      </div>
    </div>
  );
}
