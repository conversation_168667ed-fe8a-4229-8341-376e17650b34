"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  EllipsisVertical,
  Printer,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { usePageSize } from "@/components/ui/base/PageSizeSelector";
import EnhancedPagination from "@/components/ui/base/EnhancedPagination";
import { useRouter } from "next/navigation";

interface SubmissionData {
  id: number;
  nama: string;
  tanaman: string;
  jumlah: number;
  status: string;
  tanggal: string;
}

export default function TableSubmission() {
  const { getToken } = useAuth();
  const token = getToken();
  const router = useRouter();
  const { pageSize } = usePageSize();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [listSubmission, setListSubmission] = useState<SubmissionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchPage = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement penyuluh submission API call
      // For now, using mock data
      const mockData: SubmissionData[] = [
        {
          id: 1,
          nama: "Budi Santoso",
          tanaman: "Padi",
          jumlah: 100,
          status: "Disetujui",
          tanggal: "2024-01-15",
        },
        {
          id: 2,
          nama: "Siti Aminah",
          tanaman: "Jagung",
          jumlah: 50,
          status: "Sedang Diproses",
          tanggal: "2024-01-14",
        },
        {
          id: 3,
          nama: "Ahmad Wijaya",
          tanaman: "Kedelai",
          jumlah: 75,
          status: "Direvisi",
          tanggal: "2024-01-13",
        },
      ];

      setListSubmission(mockData);
      setTotalItems(mockData.length);
      setTotalPages(Math.ceil(mockData.length / pageSize));
    } catch (error) {
      console.error("Error fetching submissions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPage();
  }, [currentPage, pageSize]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Disetujui":
        return "text-green-600 bg-green-100";
      case "Sedang Diproses":
        return "text-yellow-600 bg-yellow-100";
      case "Direvisi":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const handleViewDetail = (id: number) => {
    router.push(`/home/<USER>/${id}`);
  };

  if (isLoading) {
    return (
      <div className="bg-white p-4 rounded-md shadow-md mt-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-md shadow-md mt-4">
      <div className="text-lg font-medium mb-4">Daftar Pengajuan Terbaru</div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>No</TableHead>
            <TableHead>Nama Petani</TableHead>
            <TableHead>Jenis Tanaman</TableHead>
            <TableHead>Jumlah</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Tanggal</TableHead>
            <TableHead>Aksi</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {listSubmission.map((item, index) => (
            <TableRow key={item.id}>
              <TableCell>{(currentPage - 1) * pageSize + index + 1}</TableCell>
              <TableCell>{item.nama}</TableCell>
              <TableCell>{item.tanaman}</TableCell>
              <TableCell>{item.jumlah}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status)}`}>
                  {item.status}
                </span>
              </TableCell>
              <TableCell>{new Date(item.tanggal).toLocaleDateString('id-ID')}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger>
                    <EllipsisVertical className="h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleViewDetail(item.id)}>
                      Lihat Detail
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Printer className="h-4 w-4 mr-2" />
                      Cetak
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={7}>
              <EnhancedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                onPageChange={setCurrentPage}
              />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
}
