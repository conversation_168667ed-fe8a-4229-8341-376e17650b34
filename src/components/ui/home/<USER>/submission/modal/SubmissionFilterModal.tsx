import FormSelect from "@/components/ui/base/form-select";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { fetchJenisTanamanData } from "@/lib/master/jenisTanamanFetching";
import { Jen<PERSON><PERSON><PERSON><PERSON> } from "@/types/master/jenisTanaman";
import { XIcon } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

/**
 * ModalProps is an interface for the properties of the modal component.
 * @interface
 */
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * FilterModal component renders a modal dialog with terms and conditions.
 *
 * @component
 * @param {ModalProps} props - The properties for the modal component.
 * @param {boolean} props.isOpen - Determines if the modal is open or closed.
 * @param {() => void} props.onClose - Function to call when the modal is closed.
 *
 * @returns {JSX.Element | null} The rendered modal component or null if not open.
 */
const SubmissionFilterModal: React.FC<ModalProps> = ({ isOpen, onClose }) => {
  // Move all hooks to the top, before any conditional returns
  const { getToken } = useAuth();
  const token = getToken();
  const [listPlant, setListPlant] = useState<JenisTanaman[]>([]);
  const [formData, setFormData] = useState({
    tanamanId: 0,
  });
  const [messageError, setMessageError] = useState<
    Record<keyof typeof formData, string | null>
  >({
    tanamanId: null,
  });

  const fetchListPlant = useCallback(async () => {
    const data = await fetchJenisTanamanData(1, String(token));
    setListPlant(data.items);
  }, [token]);

  useEffect(() => {
    if (isOpen) {
      fetchListPlant();
    }
  }, [isOpen, fetchListPlant]);

  // Early return after all hooks
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg p-6 w-3/4 md:w-1/2 lg:w-1/3">
        <h2 className="text-2xl font-semibold mb-4 text-center flex justify-end">
          <XIcon className="h-6 w-6 cursor-pointer" onClick={onClose} />
        </h2>

        <div className="flex flex-col gap-4 md:flex-row md:gap-6">
          <FormSelect
            label="Jenis Tanaman"
            value={listPlant.map((value) => value.name)}
            selected={
              listPlant.find((plant) => plant.id === formData.tanamanId)
                ?.name || ""
            }
            onChange={(value: string) => {
              const selectedPlant = listPlant.find(
                (plant) => plant.name === value
              );
              if (selectedPlant) {
                setFormData({
                  ...formData,
                  tanamanId: selectedPlant.id,
                });
              }
            }}
            errorMessage={messageError.tanamanId}
            required
          />
        </div>

        <div className="mt-4 flex justify-end">
          <Button
            onClick={() => {}}
            className="bg-primary-default rounded-full text-white"
          >
            Filter
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SubmissionFilterModal;
