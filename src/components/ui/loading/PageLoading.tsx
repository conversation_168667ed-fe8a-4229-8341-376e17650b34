import React from 'react';

interface PageLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const PageLoading: React.FC<PageLoadingProps> = ({ 
  message = "Loading...", 
  size = 'md',
  fullScreen = true 
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  const containerClasses = fullScreen 
    ? 'min-h-screen flex items-center justify-center bg-background'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center space-y-4">
        <div className={`animate-spin rounded-full border-b-2 border-primary-500 ${sizeClasses[size]}`}></div>
        <p className="text-sm text-gray-600 animate-pulse">{message}</p>
      </div>
    </div>
  );
};

export default PageLoading;
