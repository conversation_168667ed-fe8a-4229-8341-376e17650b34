"use client";

import * as React from "react";
import { format } from "date-fns";
import { id as localeId } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "recharts";

interface DatePickerProps {
  label: string;
  required?: boolean;
  date: Date;
  onSelect: (date: Date) => void;
  errorMessage?: string | null;
}

export default function DatePicker({
  date,
  onSelect,
  label,
  required,
  errorMessage
}: DatePickerProps) {
  return (
    <div className="flex flex-col items-start w-full gap-1.5">
      <div className="text-sm font-medium text-gray-700">
        {label} {required ? <span className="text-danger-600">*</span> : ""}
      </div>
      <div className="w-full">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal relative rounded-full min-h-[40px] px-4",
                !date && "text-muted-foreground"
              )}
            >
              <div className="min-w-[10rem]">
                {date ? (
                format(date, "dd MMMM yyyy", { locale: localeId })
              ) : (
                <span>Pilih tanggal</span>
              )}
              </div>
              <CalendarIcon className="h-4 w-4 absolute right-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 bg-white shadow-md rounded-md">
            <Calendar
              mode="single"
              selected={date}
              onSelect={(date) => date && onSelect(date)}
            />
          </PopoverContent>
        </Popover>
      </div>
      {errorMessage && (
        <div className="text-sm text-danger-600 mt-1">{errorMessage}</div>
      )}
    </div>
  );
}
