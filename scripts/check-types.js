#!/usr/bin/env node

/**
 * TypeScript Type Checking Script
 * Checks for common TypeScript errors and provides fixes
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Checking TypeScript types...\n');

// Common TypeScript fixes
const typeScriptFixes = {
  // Fix implicit any types
  'implicitly has an \'any\' type': {
    description: 'Add explicit type annotations',
    examples: [
      '// ❌ Before',
      'const items = data.map((item) => item.name);',
      '',
      '// ✅ After', 
      'const items = data.map((item: DataItem) => item.name);',
      'const items = data.map((item: any) => item.name); // temporary fix'
    ]
  },

  // Fix missing dependencies
  'missing dependency': {
    description: 'Add missing dependencies to useEffect',
    examples: [
      '// ❌ Before',
      'useEffect(() => {',
      '  fetchData();',
      '}, []);',
      '',
      '// ✅ After',
      'useEffect(() => {',
      '  fetchData();',
      '}, [fetchData]);'
    ]
  },

  // Fix React Hooks rules
  'React Hook.*called conditionally': {
    description: 'Move hooks to top of component',
    examples: [
      '// ❌ Before',
      'function Component({ isOpen }) {',
      '  if (!isOpen) return null;',
      '  const [state, setState] = useState();',
      '}',
      '',
      '// ✅ After',
      'function Component({ isOpen }) {',
      '  const [state, setState] = useState();',
      '  if (!isOpen) return null;',
      '}'
    ]
  }
};

// Run TypeScript check
try {
  console.log('Running TypeScript compiler...');
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('✅ No TypeScript errors found!');
} catch (error) {
  console.log('❌ TypeScript errors detected. See output above.');
  
  console.log('\n📋 Common fixes for TypeScript errors:\n');
  
  Object.entries(typeScriptFixes).forEach(([pattern, fix]) => {
    console.log(`🔧 ${fix.description}:`);
    fix.examples.forEach(example => {
      console.log(`   ${example}`);
    });
    console.log('');
  });
  
  console.log('💡 Quick fixes applied in this project:');
  console.log('   ✅ Added type annotations for map callbacks');
  console.log('   ✅ Fixed useEffect dependencies');
  console.log('   ✅ Moved hooks to component top');
  console.log('   ✅ Added proper error type handling');
  console.log('   ✅ Fixed SystemStatus interface types');
  
  process.exit(1);
}

console.log('\n🎉 TypeScript check completed successfully!');
