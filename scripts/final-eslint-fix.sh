#!/bin/bash

# Final ESLint Fix Script
echo "🔧 Starting final ESLint fixes..."

# Array of files to fix
files=(
  "src/app/(main)/home/<USER>/management/access/page.tsx"
  "src/app/(main)/home/<USER>/management/distribution/page.tsx"
  "src/app/(main)/home/<USER>/management/farmer/page.tsx"
  "src/app/(main)/home/<USER>/management/instructor/page.tsx"
  "src/app/(main)/home/<USER>/master/distribution-method/page.tsx"
  "src/app/(main)/home/<USER>/master/land-ownership-status/page.tsx"
  "src/app/(main)/home/<USER>/master/planting-methods/page.tsx"
  "src/app/(main)/home/<USER>/master/poktan/page.tsx"
  "src/app/(main)/home/<USER>/master/type-plant/page.tsx"
  "src/app/(main)/home/<USER>/planting/seed-stock/page.tsx"
  "src/app/(main)/home/<USER>/planting/submission-plant/page.tsx"
  "src/app/(main)/home/<USER>/report/distribution/page.tsx"
  "src/app/(main)/home/<USER>/report/plant-submission/page.tsx"
  "src/app/(main)/home/<USER>/report/submission/page.tsx"
  "src/app/(main)/home/<USER>/page.tsx"
  "src/app/(main)/home/<USER>/page.tsx"
)

# Function to fix useEffect dependencies
fix_useeffect() {
  local file="$1"
  if [ -f "$file" ]; then
    # Fix useEffect with fetchPage dependency
    sed -i '' 's/}, \[currentPage\]);/}, [currentPage, fetchPage]);/g' "$file"
    
    # Fix useEffect with empty dependency for fetchPage
    sed -i '' 's/}, \[\]);/}, [fetchPage]);/g' "$file"
    
    echo "✅ Fixed useEffect in: $file"
  else
    echo "⚠️  File not found: $file"
  fi
}

# Function to remove unused imports
remove_unused_imports() {
  local file="$1"
  if [ -f "$file" ]; then
    # Remove ChevronDown import
    sed -i '' 's/ChevronDown,//g' "$file"
    sed -i '' 's/,ChevronDown//g' "$file"
    
    # Clean up empty spaces in imports
    sed -i '' 's/,  ,/,/g' "$file"
    sed -i '' 's/{  ,/{/g' "$file"
    sed -i '' 's/,  }/}/g' "$file"
    
    echo "✅ Cleaned imports in: $file"
  fi
}

# Apply fixes to all files
for file in "${files[@]}"; do
  fix_useeffect "$file"
  remove_unused_imports "$file"
done

# Fix specific complex cases
echo "🔧 Fixing complex cases..."

# Fix [slug] pages with multiple dependencies
slug_files=(
  "src/app/(main)/home/<USER>/management/admin/[slug]/page.tsx"
  "src/app/(main)/home/<USER>/management/distribution/[slug]/page.tsx"
  "src/app/(main)/home/<USER>/management/farmer/[slug]/page.tsx"
  "src/app/(main)/home/<USER>/management/instructor/[slug]/page.tsx"
  "src/app/(main)/home/<USER>/submission/[slug]/page.tsx"
  "src/app/(main)/home/<USER>/[slug]/page.tsx"
)

for file in "${slug_files[@]}"; do
  if [ -f "$file" ]; then
    # Add missing dependencies for complex useEffect
    sed -i '' 's/}, \[\]);/}, [fetchDataUser, params.slug, token]);/g' "$file"
    echo "✅ Fixed complex useEffect in: $file"
  fi
done

# Fix report pages with callback dependencies
report_files=(
  "src/app/(main)/home/<USER>/report/distribution/page.tsx"
  "src/app/(main)/home/<USER>/report/plant-submission/page.tsx"
  "src/app/(main)/home/<USER>/report/submission/page.tsx"
)

for file in "${report_files[@]}"; do
  if [ -f "$file" ]; then
    # Fix useCallback dependencies
    sed -i '' 's/}, \[\]);/}, [fetchDataJenisTanaman]);/g' "$file"
    echo "✅ Fixed useCallback in: $file"
  fi
done

# Fix specific files
echo "🔧 Fixing specific files..."

# Fix log page
if [ -f "src/app/(main)/home/<USER>/page.tsx" ]; then
  sed -i '' 's/}, \[\]);/}, [dateParent.from, dateParent.to, fetchPage]);/g' "src/app/(main)/home/<USER>/page.tsx"
  echo "✅ Fixed log page"
fi

# Fix profile [slug] page
if [ -f "src/app/(main)/home/<USER>/[slug]/page.tsx" ]; then
  sed -i '' 's/}, \[\]);/}, [fetchPage, pathname]);/g' "src/app/(main)/home/<USER>/[slug]/page.tsx"
  echo "✅ Fixed profile [slug] page"
fi

# Fix map component
if [ -f "src/components/ui/base/map.tsx" ]; then
  sed -i '' 's/}, \[\]);/}, [center, status]);/g' "src/components/ui/base/map.tsx"
  echo "✅ Fixed map component"
fi

# Fix dashboard components
dashboard_components=(
  "src/components/ui/home/<USER>/dashboard/components/admin/tab-submission.tsx"
  "src/components/ui/home/<USER>/dashboard/components/admin/table-submission.tsx"
  "src/components/ui/home/<USER>/dashboard/components/distributor/tab-submission.tsx"
  "src/components/ui/home/<USER>/dashboard/components/distributor/table-submission.tsx"
)

for file in "${dashboard_components[@]}"; do
  if [ -f "$file" ]; then
    sed -i '' 's/}, \[\]);/}, [fetchPage]);/g' "$file"
    echo "✅ Fixed dashboard component: $file"
  fi
done

# Fix planting components
if [ -f "src/components/ui/home/<USER>/planting/components/AddSeedStok.tsx" ]; then
  sed -i '' 's/}, \[\]);/}, [currentPage]);/g' "src/components/ui/home/<USER>/planting/components/AddSeedStok.tsx"
  echo "✅ Fixed AddSeedStok component"
fi

if [ -f "src/components/ui/home/<USER>/planting/components/DetailSeedStok.tsx" ]; then
  sed -i '' 's/}, \[\]);/}, [token]);/g' "src/components/ui/home/<USER>/planting/components/DetailSeedStok.tsx"
  sed -i '' 's/}, \[id\]);/}, [fetchData, id]);/g' "src/components/ui/home/<USER>/planting/components/DetailSeedStok.tsx"
  echo "✅ Fixed DetailSeedStok component"
fi

echo "✨ All ESLint fixes completed!"
echo "📝 Please run 'npm run lint' to verify all fixes."
