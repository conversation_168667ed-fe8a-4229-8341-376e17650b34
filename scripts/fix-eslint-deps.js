#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically fix common ESLint useEffect dependency warnings
 * This script will add missing dependencies to useEffect hooks
 */

const fs = require('fs');
const path = require('path');

// Files that need fixing based on the error log
const filesToFix = [
  'src/app/(main)/home/<USER>/distribution/page.tsx',
  'src/app/(main)/home/<USER>/management/access/page.tsx',
  'src/app/(main)/home/<USER>/management/admin/page.tsx',
  'src/app/(main)/home/<USER>/management/distribution/page.tsx',
  'src/app/(main)/home/<USER>/management/farmer/page.tsx',
  'src/app/(main)/home/<USER>/management/instructor/page.tsx',
  'src/app/(main)/home/<USER>/master/distribution-method/page.tsx',
  'src/app/(main)/home/<USER>/master/land-ownership-status/page.tsx',
  'src/app/(main)/home/<USER>/master/planting-methods/page.tsx',
  'src/app/(main)/home/<USER>/master/poktan/page.tsx',
  'src/app/(main)/home/<USER>/master/type-plant/page.tsx',
  'src/app/(main)/home/<USER>/planting/seed-stock/page.tsx',
  'src/app/(main)/home/<USER>/planting/submission-plant/page.tsx',
  'src/app/(main)/home/<USER>/report/distribution/page.tsx',
  'src/app/(main)/home/<USER>/report/plant-submission/page.tsx',
  'src/app/(main)/home/<USER>/report/submission/page.tsx',
  'src/app/(main)/home/<USER>/page.tsx',
  'src/app/(main)/home/<USER>/page.tsx',
];

function fixUseEffectDependencies(content) {
  // Pattern 1: useEffect with fetchPage dependency
  content = content.replace(
    /useEffect\(\(\) => \{\s*fetchPage\(currentPage\);\s*\}, \[currentPage\]\);/g,
    'useEffect(() => {\n    fetchPage(currentPage);\n  }, [currentPage, fetchPage]);'
  );

  // Pattern 2: useEffect with missing fetchPage dependency (different format)
  content = content.replace(
    /useEffect\(\(\) => \{\s*fetchPage\(\d+\);\s*\}, \[\]\);/g,
    (match) => {
      const pageNumber = match.match(/fetchPage\((\d+)\)/)[1];
      return `useEffect(() => {\n    fetchPage(${pageNumber});\n  }, [fetchPage]);`;
    }
  );

  return content;
}

function addUseCallbackToFetchPage(content) {
  // Look for fetchPage function definitions and wrap them in useCallback
  const fetchPagePattern = /const fetchPage = async \(([^)]*)\) => \{([^}]+)\};/g;
  
  content = content.replace(fetchPagePattern, (match, params, body) => {
    // Extract dependencies from the function body
    const dependencies = [];
    
    if (body.includes('token')) dependencies.push('token');
    if (body.includes('loading')) dependencies.push('loading');
    if (body.includes('setLoading')) dependencies.push('setLoading');
    
    const depsString = dependencies.length > 0 ? dependencies.join(', ') : '';
    
    return `const fetchPage = useCallback(async (${params}) => {${body}}, [${depsString}]);`;
  });

  return content;
}

function addImports(content) {
  // Add useCallback import if not present
  if (content.includes('useEffect') && !content.includes('useCallback')) {
    content = content.replace(
      /import { ([^}]+) } from "react";/,
      (match, imports) => {
        if (!imports.includes('useCallback')) {
          return `import { ${imports}, useCallback } from "react";`;
        }
        return match;
      }
    );
  }

  return content;
}

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply fixes
    content = addImports(content);
    content = addUseCallbackToFetchPage(content);
    content = fixUseEffectDependencies(content);

    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

// Main execution
console.log('🔧 Starting ESLint dependency fixes...\n');

filesToFix.forEach(fixFile);

console.log('\n✨ ESLint dependency fixes completed!');
console.log('\n📝 Manual fixes still needed:');
console.log('- Complex useEffect hooks with multiple dependencies');
console.log('- Form data dependencies in [slug] pages');
console.log('- Callback dependencies in report pages');
