#!/usr/bin/env node

/**
 * Script to clean unused imports and variables
 */

const fs = require('fs');

function cleanUnusedImports(content) {
  // Remove unused imports
  const unusedImports = [
    'ChevronDown',
    'fetchPoktanData',
  ];

  unusedImports.forEach(importName => {
    // Remove from import statements
    content = content.replace(
      new RegExp(`\\s*${importName},?\\s*`, 'g'),
      ''
    );
    
    // Clean up empty lines in imports
    content = content.replace(/,\s*,/g, ',');
    content = content.replace(/{\s*,/g, '{');
    content = content.replace(/,\s*}/g, '}');
  });

  return content;
}

function cleanUnusedVariables(content) {
  // Remove unused state variables
  const unusedStates = [
    'const \\[isOpen, setIsOpen\\] = useState\\(false\\);',
    'const \\[status, setStatus\\] = useState\\(""\\);',
    'const \\[id, setId\\] = useState\\(0\\);',
  ];

  unusedStates.forEach(pattern => {
    content = content.replace(new RegExp(pattern, 'g'), '');
  });

  // Clean up extra newlines
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

  return content;
}

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    content = cleanUnusedImports(content);
    content = cleanUnusedVariables(content);

    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Cleaned: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error cleaning ${filePath}:`, error.message);
  }
}

// Clean distribution page
fixFile('src/app/(main)/home/<USER>/distribution/page.tsx');

console.log('✨ Cleanup completed!');
