#!/usr/bin/env node

/**
 * Comprehensive ESLint fixes for all files
 */

const fs = require('fs');
const path = require('path');

// Common patterns to fix
const fixes = {
  // Fix useEffect with fetchPage dependency
  useEffectFetchPage: {
    pattern: /useEffect\(\(\) => \{\s*fetchPage\(currentPage\);\s*\}, \[currentPage\]\);/g,
    replacement: 'useEffect(() => {\n    fetchPage(currentPage);\n  }, [currentPage, fetchPage]);'
  },

  // Fix useEffect with empty dependency array for fetchPage
  useEffectFetchPageEmpty: {
    pattern: /useEffect\(\(\) => \{\s*fetchPage\(\d+\);\s*\}, \[\]\);/g,
    replacement: (match) => {
      const pageNumber = match.match(/fetchPage\((\d+)\)/)[1];
      return `useEffect(() => {\n    fetchPage(${pageNumber});\n  }, [fetchPage]);`;
    }
  },

  // Fix useCallback missing dependencies for fetchDataJenisTanaman
  useCallbackFetchData: {
    pattern: /useCallback\(async \(\) => \{\s*await fetchDataJenisTanaman\(\);\s*\}, \[\]\);/g,
    replacement: 'useCallback(async () => {\n    await fetchDataJenisTanaman();\n  }, [fetchDataJenisTanaman]);'
  },

  // Add useCallback import if missing
  addUseCallbackImport: {
    pattern: /import { ([^}]*) } from "react";/,
    replacement: (match, imports) => {
      if (!imports.includes('useCallback')) {
        return `import { ${imports}, useCallback } from "react";`;
      }
      return match;
    }
  },

  // Remove unused imports
  removeUnusedImports: [
    'ChevronDown',
    'fetchPoktanData',
    'ROLE_PERMISSIONS',
    'useUserStore'
  ],

  // Escape unescaped entities
  escapeEntities: {
    pattern: /You don't have/g,
    replacement: "You don&apos;t have"
  }
};

function applyFixes(content) {
  let fixedContent = content;

  // Apply regex fixes
  Object.keys(fixes).forEach(fixName => {
    const fix = fixes[fixName];
    if (fix.pattern && fix.replacement) {
      if (typeof fix.replacement === 'function') {
        fixedContent = fixedContent.replace(fix.pattern, fix.replacement);
      } else {
        fixedContent = fixedContent.replace(fix.pattern, fix.replacement);
      }
    }
  });

  // Remove unused imports
  fixes.removeUnusedImports.forEach(importName => {
    // Remove from import statements
    fixedContent = fixedContent.replace(
      new RegExp(`\\s*${importName},?\\s*`, 'g'),
      ''
    );
  });

  // Clean up import formatting
  fixedContent = fixedContent.replace(/,\s*,/g, ',');
  fixedContent = fixedContent.replace(/{\s*,/g, '{');
  fixedContent = fixedContent.replace(/,\s*}/g, '}');

  // Clean up extra newlines
  fixedContent = fixedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

  return fixedContent;
}

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    content = applyFixes(content);

    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

// Files to fix (from the error log)
const filesToFix = [
  'src/app/(main)/home/<USER>/distribution/page.tsx',
  'src/app/(main)/home/<USER>/management/access/page.tsx',
  'src/app/(main)/home/<USER>/management/admin/page.tsx',
  'src/app/(main)/home/<USER>/management/distribution/page.tsx',
  'src/app/(main)/home/<USER>/management/farmer/page.tsx',
  'src/app/(main)/home/<USER>/management/instructor/page.tsx',
  'src/app/(main)/home/<USER>/master/distribution-method/page.tsx',
  'src/app/(main)/home/<USER>/master/land-ownership-status/page.tsx',
  'src/app/(main)/home/<USER>/master/planting-methods/page.tsx',
  'src/app/(main)/home/<USER>/master/poktan/page.tsx',
  'src/app/(main)/home/<USER>/master/type-plant/page.tsx',
  'src/app/(main)/home/<USER>/planting/seed-stock/page.tsx',
  'src/app/(main)/home/<USER>/planting/submission-plant/page.tsx',
  'src/app/(main)/home/<USER>/report/distribution/page.tsx',
  'src/app/(main)/home/<USER>/report/plant-submission/page.tsx',
  'src/app/(main)/home/<USER>/report/submission/page.tsx',
  'src/app/(main)/home/<USER>/page.tsx',
  'src/app/(main)/home/<USER>/page.tsx',
  'src/components/ui/home/<USER>'
];

console.log('🔧 Starting comprehensive ESLint fixes...\n');

filesToFix.forEach(fixFile);

console.log('\n✨ ESLint fixes completed!');
console.log('\n📝 Manual review needed for:');
console.log('- Complex form dependencies in [slug] pages');
console.log('- Multi-parameter useEffect hooks');
console.log('- Custom callback dependencies');
