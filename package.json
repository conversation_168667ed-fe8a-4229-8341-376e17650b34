{"name": "sisarpas-front-end", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 9000", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "prepare": "husky && husky install", "format": "prettier --write ."}, "dependencies": {"@abak/react-image-picker": "^1.1.4", "@coreui/coreui": "^5.3.2", "@coreui/coreui-pro": "^5.12.0", "@coreui/react": "^5.6.0", "@coreui/react-pro": "^5.14.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "4", "@tanstack/react-query-devtools": "^5.65.1", "@tanstack/react-table": "^8.19.3", "animate.css": "^4.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lucide-react": "^0.418.0", "next": "^14.2.24", "quill": "^2.0.3", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-quill": "^2.0.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-testing-library": "^6.2.2", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.4.3", "postcss": "^8", "prettier": "3.3.3", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.3", "ts-node": "^10.9.2", "typescript": "^5"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}