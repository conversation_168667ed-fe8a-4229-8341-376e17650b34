name: <PERSON><PERSON> with Reviewdog (Hybrid Mode)

on:
  pull_request:
    branches: [main]

permissions:
  contents: read
  pull-requests: write # penting untuk allow PR comment

jobs:
  lint:
    name: Run next lint with reviewdog
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run reviewdog with PR comment
        id: reviewdog_pr
        continue-on-error: true
        uses: reviewdog/action-eslint@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-pr-review
          fail_on_error: false
          eslint_flags: '.'

      - name: Run reviewdog fallback to GitHub Check if PR comment fails
        if: steps.reviewdog_pr.outcome == 'failure'
        uses: reviewdog/action-eslint@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-check
          fail_on_error: false
          eslint_flags: '.'
