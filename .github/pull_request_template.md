## 🧾 Ringkasan PR
Jelaskan secara singkat perubahan apa yang dilakukan.

Contoh:
- Menambahkan komponen Button
- Memperbaiki bug validasi login
- Menghapus file tidak terpakai


### Screenshoot (jika perubahan desain)
file screenshoot yang di kerjakan

---

## ✅ Checklist Developer (WAJIB DICEK SEBELUM REQUEST REVIEW)
Silakan centang semua checklist di bawah sebelum mengajukan PR:

- [ ] Saya menggunakan **Node.js versi >= 22** sesuai `.nvmrc`
- [ ] Saya menggunakan **pnpm versi >= 9**
- [ ] <PERSON><PERSON> su<PERSON> menja<PERSON> `pnpm install` dan tidak ada error
- [ ] <PERSON><PERSON> sudah menjalankan `pnpm lint` / `next lint` tanpa warning/error
- [ ] PR ini tidak mengandung file sensitif (contoh: `.env`, `node_modules`, `dist`, dsb)

---

## 🔍 Catatan Reviewer
Jika ada hal yang perlu diperhatikan reviewer, tulis di sini.
Contoh: "Komponen ini butuh pengecekan di dark mode."
